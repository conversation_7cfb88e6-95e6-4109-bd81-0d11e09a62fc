import '../../db/db.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class PutProdutos {
  final SupabaseClient _supabase = Supabase.instance.client;
  DB db = DB();

  Future<void> likeProduct(int productId, String userEmail) async {
    final response = await _supabase.from(db.tabelaDeProdutosLikes).insert({
      'product_id': productId,
      'user_email': userEmail, // Passar o email do usuário autenticado
    });

    if (response != null) {
      throw Exception('Erro ao curtir o produto: $response');
    }
  }

  Future<void> unlikeProduct(int productId) async {
    final response = await _supabase
        .from(db.tabelaDeProdutosLikes)
        .delete()
        .match({'product_id': productId});
    if (response != null) {
      throw Exception('Erro ao descurtir o produto: $response');
    }
  }

  Future<void> salvarProduto(int productId, String userEmail) async {
    try {
      await _supabase.from(db.tabelaDeProdutosSalvos).insert({
        'product_id': productId,
        'user_email': userEmail,
      });
    } catch (e) {
      throw Exception('Erro ao salvar produto: $e');
    }
  }

  Future<void> removerProdutoSalvo(
    int productId,
    String userEmail,
  ) async {
    try {
      await _supabase.from(db.tabelaDeProdutosSalvos).delete().match({
        'product_id': productId,
        'user_email': userEmail,
      });
    } catch (e) {
      throw Exception('Erro ao remover produto salvo: $e');
    }
  }

  Future<void> removerTodosProdutosSalvos(String userEmail) async {
    try {
      await _supabase.from(db.tabelaDeProdutosSalvos).delete().match({
        'user_email': userEmail,
      });
    } catch (e) {
      throw Exception('Erro ao remover todos os produtos salvos: $e');
    }
  }
}
