import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/components/black_and_white_filter.dart';
import 'package:promobell/src/components/flag_disabled.dart';
import 'package:promobell/src/models/notification_storage_service.dart';
import 'package:promobell/src/models/product.dart';
import 'package:promobell/src/modules/offers/controllers/notification_controller.dart';
import 'package:promobell/src/modules/offers/controllers/offers_controller.dart';
import 'package:promobell/src/modules/offers/offers_module.dart';
import 'package:promobell/src/modules/offers/ui/widget/offers_page/benifit_card.dart';
import 'package:promobell/src/modules/offers/ui/widget/product_details/product_image_box.dart';
import 'package:promobell/src/modules/offers/ui/widget/product_details/styled_logo_container.dart';
import 'package:promobell/src/services/logs/app_logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../helpers/plataform_icons.dart';
import 'dotted_line.dart';

class CardNotificationStorage extends StatelessWidget {
  const CardNotificationStorage({
    super.key,
    required this.title,
    required this.body,
    required this.date,
    required this.read,
    this.id,
    this.idProduto,
    this.email,
  });

  final String title;
  final String body;
  final String date;
  final int read;
  final int? id;
  final int? idProduto;
  final String? email;

  @override
  Widget build(BuildContext context) {
    if (idProduto != null) {
      return FutureBuilder<Product>(
        future: getProduct(idProduto!.toString()),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return _buildBasicNotification(context); // fallback
          } else {
            return _buildProductNotification(context, snapshot.data!);
          }
        },
      );
    } else {
      return _buildBasicNotification(context);
    }
  }

  Widget _buildBasicNotification(BuildContext context) {
    return FutureBuilder<SharedPreferences>(
      future: SharedPreferences.getInstance(),
      builder: (context, snapshot) {
        final userName = snapshot.data?.getString('userName') ?? 'Promolover';

        return Container(
          decoration: const BoxDecoration(color: ColorOutlet.paper),
          child: Column(
            children: [
              _buildHeader(),
              const SizedBox(height: 4),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: MediaQuery.of(context).size.width - 48,
                    child: TextPattern.customText(
                      text: title.replaceAll('{name}', userName),
                      fontSize: 16,
                      maxLines: 2,
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                      fontWeightOption: FontWeightOption.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.only(right: 12),
                    child: TextPattern.customText(
                      text: body.replaceAll('{name}', userName),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              const DottedLine(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProductNotification(
    BuildContext context,
    Product product,
  ) {
    final OffersController controller = Modular.get<OffersController>();
    final NotificationController controllerNotification =
        Modular.get<NotificationController>();

    return FutureBuilder<SharedPreferences>(
      future: SharedPreferences.getInstance(),
      builder: (context, snapshot) {
        final userName = snapshot.data?.getString('userName') ?? 'Promolover';
        final personalizedTitle = title.replaceAll(
          '{name}',
          userName,
        );
        final personalizedBody = body.replaceAll('{name}', userName);

        return InkWell(
          onTap: () {
            Modular.to.pushNamed(
              '/offers${OffersModule.productDetails}',
              arguments: product,
            );
            controllerNotification.setReadNotification(
              NotificationStorageService(
                body: body,
                title: title,
                date: date,
                idProduto: idProduto,
                email: email,
                id: id,
              ),
            );
          },
          child: Container(
            decoration: const BoxDecoration(color: ColorOutlet.paper),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 4),
                TextPattern.customText(
                  text: personalizedTitle,
                  fontSize: 16,
                  maxLines: 2,
                  fontWeightOption: FontWeightOption.bold,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                TextPattern.customText(
                  text: personalizedBody,
                  fontSize: 14,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Stack(
                      children: [
                        product.invalidProduct
                            ? Stack(
                                children: [
                                  BlackAndWhiteFilter(
                                    child: ProductImageBox(
                                      product: product,
                                      height: 120,
                                      width: 120,
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                      top: 44,
                                    ),
                                    child: FlagDisabled(
                                      height: 32,
                                      text: 'Indisponível',
                                      width: 120,
                                      fontWeightOption: FontWeightOption.medium,
                                    ),
                                  ),
                                ],
                              )
                            : ProductImageBox(
                                product: product,
                                height: 120,
                                width: 120,
                              ),
                        Positioned(
                          top: 8,
                          left: 8,
                          child: StyledLogoContainer(
                            padding: 4,
                            borderRadius: 8,
                            logo: PlatformIcons.fromName(
                              product.plataforma,
                            ),
                            plataformName: product.plataforma,
                            height: 24,
                            width: 24,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              IntrinsicWidth(
                                child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: BenefitCard(
                                    ofertaIndisponivel: product.invalidProduct,
                                    cupom: product.cupom,
                                    frete: product.frete,
                                    menorPreco: product.menorPreco,
                                    plataforma: product.plataforma,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 6),
                          TextPattern.customText(
                            text: product.titulo,
                            fontSize: 14,
                            maxLines: 2,
                            fontWeightOption: FontWeightOption.bold,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 6),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (product.precoAntigo > product.precoAtual)
                                TextPattern.customText(
                                  text: controller.convertDoubleToString(
                                    product.precoAntigo,
                                  ),
                                  fontSize: 12,
                                  color: ColorOutlet.contentGhost,
                                  decoration: TextDecoration.lineThrough,
                                  decorationColor: ColorOutlet.contentGhost,
                                ),
                              const SizedBox(width: 8),
                              TextPattern.customText(
                                text: controller.convertDoubleToString(
                                  product.precoAtual,
                                ),
                                fontSize: 20,
                                color: ColorOutlet.contentPrimary,
                                fontWeightOption: FontWeightOption.bold,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const DottedLine(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: getRead(read)
                  ? ColorOutlet.contentGhost
                  : ColorOutlet.contentPrimary,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 16),
          TextPattern.customText(
            text: formatDateTimeNotification(date),
            color: ColorOutlet.contentGhost,
            fontSize: 12,
          ),
        ],
      ),
    );
  }

  bool getRead(int read) => read != 0;

  String formatDateTimeNotification(String date) {
    final dateTime = DateTime.parse(date);
    final now = DateTime.now();

    if (dateTime.year == now.year &&
        dateTime.month == now.month &&
        dateTime.day == now.day) {
      return '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (dateTime.difference(now).inDays == -1) {
      return 'Ontem';
    } else {
      return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
    }
  }

  Future<Product> getProduct(String id) async {
    final int idProduto = int.parse(id);
    final supabase = Supabase.instance.client;

    try {
      final data = await supabase
          .from('produtos_cadastro')
          .select()
          .eq('id', idProduto)
          .select();

      if (data.isEmpty) {
        throw Exception('Produto não encontrado');
      }

      final Product produto = Product.fromMap(data.first);
      return produto;
    } catch (e, stackTrace) {
      AppLogger.logError('Erro ao buscar produto', e, stackTrace);
      rethrow;
    }
  }
}
