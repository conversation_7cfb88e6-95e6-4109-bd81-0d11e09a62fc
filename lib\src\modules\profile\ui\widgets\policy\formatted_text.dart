import 'package:flutter/material.dart';
import 'package:promobell/src/modules/profile/ui/widgets/policy/text_formated.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/text_pattern.dart';

class FormattedText extends StatelessWidget {
  final String? text;
  final List<String>? listItems;
  final double bottomPadding;
  final double topPadding;
  final bool selectable;
  final bool isList;
  final bool isPrimaryColor;

  const FormattedText({
    this.text,
    this.listItems,
    this.bottomPadding = 24,
    this.topPadding = 0,
    this.selectable = true,
    this.isList = false,
    this.isPrimaryColor = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (isList && listItems != null) {
      return Padding(
        padding: EdgeInsets.only(top: topPadding, bottom: bottomPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: listItems!
              .map((item) => _buildSingleLine(context, '• $item'))
              .toList(),
        ),
      );
    }

    final span = TextSpan(
      children: TextFormatter.buildTextSpans(
        text ?? '',
        highlightPrivacyPolicy: isPrimaryColor,
      ),
      style: TextStyle(
        height: 1.5,
        fontSize: 14,
        color: ColorOutlet.contentSecondary,
        fontFamily: TextPattern().fontFamily,
      ),
    );

    return Padding(
      padding: EdgeInsets.only(top: topPadding, bottom: bottomPadding),
      child: selectable ? SelectableText.rich(span) : RichText(text: span),
    );
  }

  Widget _buildSingleLine(BuildContext context, String line) {
    final span = TextSpan(
      children: TextFormatter.buildTextSpans(
        line,
        highlightPrivacyPolicy: isPrimaryColor,
      ),
      style: TextStyle(
        height: 1.5,
        fontSize: 14,
        color: ColorOutlet.contentSecondary,
        fontFamily: TextPattern().fontFamily,
      ),
    );
    return selectable ? SelectableText.rich(span) : RichText(text: span);
  }
}
