import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../controllers/profile_controller.dart';

class ProfileAvatar extends StatelessWidget {
  final String? image;
  final bool isEditing;
  final VoidCallback onTap;
  final ProfileController controller;
  final VoidCallback? onTapImage;
  const ProfileAvatar({
    super.key,
    required this.image,
    required this.isEditing,
    required this.onTap,
    required this.controller,
    required this.onTapImage,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: ColorOutlet.paper, width: 4),
      ),
      child: SizedBox(
        height: 80,
        width: 80,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: image?.isNotEmpty == true
              ? isEditing
                    ? GestureDetector(
                        onTap: onTap,
                        child: Stack(
                          children: [
                            controller.tempImageWeb != null
                                ? Image.memory(
                                    controller.tempImageWeb!,
                                    fit: BoxFit.cover,
                                    height: 80,
                                    width: 80,
                                  )
                                : Image.network(
                                    image!,
                                    fit: BoxFit.cover,
                                    height: 80,
                                    width: 80,
                                    errorBuilder:
                                        (context, error, stackTrace) =>
                                            _buildDefaultAvatar(
                                              onTap: onTap,
                                              isEditing: isEditing,
                                            ),
                                    loadingBuilder:
                                        (
                                          context,
                                          child,
                                          loadingProgress,
                                        ) => loadingProgress != null
                                        ? _buildDefaultAvatar(
                                            onTap: onTap,
                                            isEditing: isEditing,
                                          )
                                        : child,
                                  ),
                            Container(
                              color: Color(
                                0xFFFAFAFC,
                              ).withValues(alpha: 0.8),
                              alignment: Alignment.center,
                              child: SvgPicture.asset(
                                SvgIcons.archiveCamera,
                                height: 32,
                                width: 32,
                              ),
                            ),
                          ],
                        ),
                      )
                    : controller.tempImageWeb != null
                    ? Image.memory(
                        controller.tempImageWeb!,
                        fit: BoxFit.cover,
                        height: 80,
                        width: 80,
                      )
                    : Image.network(
                        image!,
                        fit: BoxFit.cover,
                        height: 80,
                        width: 80,
                        errorBuilder: (context, error, stackTrace) =>
                            _buildDefaultAvatar(
                              onTap: onTap,
                              isEditing: isEditing,
                            ),
                        loadingBuilder:
                            (
                              context,
                              child,
                              loadingProgress,
                            ) {
                              if (loadingProgress == null) {
                                return child;
                              }
                              return _buildDefaultAvatar(
                                onTap: onTap,
                                isEditing: isEditing,
                              );
                            },
                      )
              : controller.tempImageWeb != null
              ? Image.memory(
                  controller.tempImageWeb!,
                  fit: BoxFit.cover,
                  height: 80,
                  width: 80,
                )
              : _buildDefaultAvatar(
                  onTap: onTap,
                  isEditing: isEditing,
                ),
        ),
      ),
    );
  }

  Widget _buildDefaultAvatar({
    required VoidCallback onTap,
    required bool isEditing,
  }) {
    return InkWell(
      onTap: isEditing ? onTap : null,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: ColorOutlet.surface,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(color: ColorOutlet.surface, width: 4),
        ),
        child: Center(
          child: SvgPicture.asset(
            SvgIcons.archiveAvatar,
            fit: BoxFit.cover,
            height: 32,
            width: 32,
          ),
        ),
      ),
    );
  }
}
