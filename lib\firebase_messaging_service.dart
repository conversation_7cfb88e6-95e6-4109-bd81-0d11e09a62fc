import 'dart:async';

import 'package:firebase_app_installations/firebase_app_installations.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/models/product.dart';
import 'package:promobell/src/modules/offers/offers_module.dart';
import 'package:promobell/src/services/logs/app_logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'received_notification.dart';

class FirebaseMessagingService extends ChangeNotifier {
  static final FirebaseMessagingService _instance = FirebaseMessagingService._internal();
  factory FirebaseMessagingService() => _instance;
  FirebaseMessagingService._internal();

  final _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  static const MethodChannel _channel = MethodChannel(
    'br.com.promobell/notifications',
  );

  Future<void> initNotification() async {
    try {
      await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: true,
        criticalAlert: true,
      );

      await _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()?.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
        critical: true,
      );

      await _initLocalNotifications();
      await _setupInteractedMessage();
      await getToken();
      await _firebaseMessaging.subscribeToTopic('all');

      _channel.setMethodCallHandler((call) async {
        if (call.method == 'onNotificationClicked') {
          final args = call.arguments as Map<dynamic, dynamic>;
          final route = args['route'] as String?;

          if (route != null && route.isNotEmpty) {
            if (kDebugMode) {
              print('Notificação clicada via canal nativo: $route');
            }

            await Future.delayed(const Duration(milliseconds: 500));
            handleNotificationRoute(route);
          }
        }
        return null;
      });

      await _checkInitialNotificationFromPlatform();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> getInstallationId() async {
    final installations = FirebaseInstallations.instance;
    final id = await installations.getId();
    if (kDebugMode) print('Firebase Installation ID: $id');
  }

  Future<String?> getToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      if (kDebugMode) print('Firebase Token: $token');
      return token;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> _setupInteractedMessage() async {
    await _firebaseMessaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      if (kDebugMode) {
        print(
          'App aberto por notificação inicial: ${initialMessage.data}',
        );
      }
      _handleMessage(initialMessage);

      Future.delayed(const Duration(milliseconds: 1000), () {
        final route = initialMessage.data['route'];
        if (route != null && route.isNotEmpty) {
          handleNotificationRoute(route);
        }
      });
    }

    FirebaseMessaging.onMessage.listen(_handleMessage);

    FirebaseMessaging.onMessageOpenedApp.listen((message) {
      if (kDebugMode) {
        print(
          'Notificação clicada (onMessageOpenedApp): ${message.data}',
        );
      }

      final route = message.data['route'];
      if (route != null && route.isNotEmpty) {
        Future.delayed(const Duration(milliseconds: 500), () {
          handleNotificationRoute(route);
        });
      } else {
        if (kDebugMode) {
          AppLogger.logInfo(
            "Notificação tocada sem route no onMessageOpenedApp",
          );
        }
      }
    });
  }

  Future<void> handleInitialNotification() async {
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      if (kDebugMode) {
        print(
          'App aberto por notificação inicial (handleInitialNotification): ${initialMessage.data}',
        );
      }

      final payload = initialMessage.data['route'];
      if (payload != null) {
        await Future.delayed(const Duration(milliseconds: 1500));
        handleNotificationRoute(payload);
      }
    }
  }

  // Future<void> _handleMessage(RemoteMessage message) async {
  //   final data = message.data;
  //   final title = data['title'] ?? message.notification?.title;
  //   final body = data['body'] ?? message.notification?.body;

  //   await showNotification(
  //     ReceivedNotification(
  //       id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
  //       title: title,
  //       body: body,
  //       payload: data['route'] ?? '',
  //     ),
  //   );
  // }

  Future<void> _handleMessage(RemoteMessage message) async {
    final data = message.data;
    final title = data['title'];
    final body = data['body'];

    // Apenas exibe notificação se não veio por canal "notification"
    if (message.notification == null) {
      await showNotification(
        ReceivedNotification(
          id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
          title: title,
          body: body,
          payload: data['route'] ?? '',
        ),
      );
    } else {
      if (kDebugMode) {
        print('Notificação já exibida automaticamente pelo Firebase');
      }
    }
  }

  Future<void> _initLocalNotifications() async {
    const android = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    final iOS = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
      requestCriticalPermission: true,
      defaultPresentAlert: true,
      defaultPresentBadge: true,
      defaultPresentSound: true,
      defaultPresentBanner: true,
      defaultPresentList: true,
    );

    final settings = InitializationSettings(
      android: android,
      iOS: iOS,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      settings,
      onDidReceiveNotificationResponse:
          (
            NotificationResponse response,
          ) {
            if (kDebugMode) {
              print(
                'Notificação clicada (onDidReceiveNotificationResponse): ${response.payload}',
              );
            }
            final payload = response.payload;
            if (payload != null && payload.isNotEmpty) {
              handleNotificationRoute(payload);
            }
          },
      onDidReceiveBackgroundNotificationResponse: notificationTapBackground,
    );

    final notificationAppLaunchDetails = await _flutterLocalNotificationsPlugin.getNotificationAppLaunchDetails();

    if (notificationAppLaunchDetails?.didNotificationLaunchApp ?? false) {
      if (kDebugMode) {
        print(
          'App aberto por notificação local: ${notificationAppLaunchDetails?.notificationResponse?.payload}',
        );
      }

      final payload = notificationAppLaunchDetails?.notificationResponse?.payload;
      if (payload != null && payload.isNotEmpty) {
        await Future.delayed(const Duration(milliseconds: 1500));
        handleNotificationRoute(payload);
      }
    }
  }

  Future<void> showNotification(
    ReceivedNotification receivedNotification,
  ) async {
    const androidDetails = AndroidNotificationDetails(
      'lembretes_notifications_status_compras',
      'status_compras',
      channelDescription: 'Notificações de status de compras',
      importance: Importance.max,
      priority: Priority.max,
      color: Color(0xFF4141E1),
      enableVibration: true,
      enableLights: true,
      visibility: NotificationVisibility.public,
      icon: '@drawable/notification',
      category: AndroidNotificationCategory.alarm,
      channelShowBadge: true,
      colorized: true,
    );

    final iosDetails = DarwinNotificationDetails(
      threadIdentifier: 'thread_id',
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      presentBanner: true,
      presentList: true,
      interruptionLevel: InterruptionLevel.active,
      categoryIdentifier: 'PRODUCT_CATEGORY',
      attachments: <DarwinNotificationAttachment>[],
      sound: 'default',
    );

    final platformDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _flutterLocalNotificationsPlugin.show(
      receivedNotification.id,
      receivedNotification.title,
      receivedNotification.body,
      platformDetails,
      payload: receivedNotification.payload,
    );

    if (kDebugMode) print('Notification showed');
  }

  Future<void> handleNotificationRoute(String? payload) async {
    if (kDebugMode) {
      AppLogger.logInfo('route payload: $payload');
      print('Processando rota da notificação: $payload');
    }

    if (payload == null || payload.isEmpty) return;

    try {
      if (payload.contains('/product?id=')) {
        final id = payload.split('id=').last;
        if (kDebugMode) {
          print('ID do produto extraído: $id');
        }

        final product = await getProduct(id);

        if (kDebugMode) {
          print('Produto encontrado: ${product.titulo}');
          print(
            'Navegando para: /offers${OffersModule.productDetails}',
          );
        }

        await Future.delayed(const Duration(milliseconds: 500));

        WidgetsBinding.instance.addPostFrameCallback((_) {
          Modular.to.navigate('/home');
          Future.delayed(const Duration(milliseconds: 500), () {
            Modular.to.pushNamed(
              '/offers${OffersModule.productDetails}',
              arguments: product,
            );
          });
        });
      } else {
        await Future.delayed(const Duration(milliseconds: 500));

        WidgetsBinding.instance.addPostFrameCallback((_) {
          Modular.to.navigate('/home');
          Future.delayed(const Duration(milliseconds: 500), () {
            Modular.to.pushNamed(payload);
          });
        });
      }
    } catch (e, stack) {
      AppLogger.logError(
        'Erro ao navegar pela notificação',
        e,
        stack,
      );
      if (kDebugMode) {
        print('Erro ao processar rota da notificação: $e');
        print('Stack trace: $stack');
      }

      WidgetsBinding.instance.addPostFrameCallback((_) {
        Modular.to.navigate('/home');
      });
    }
  }

  Future<Product> getProduct(String id) async {
    final int idProduto = int.parse(id);
    final supabase = Supabase.instance.client;

    try {
      final data = await supabase.from('produtos_cadastro').select().eq('id', idProduto).select();
      if (data.isEmpty) throw Exception('Produto não encontrado');
      return Product.fromMap(data.first);
    } catch (e, stackTrace) {
      AppLogger.logError('Erro ao buscar produto', e, stackTrace);
      rethrow;
    }
  }

  Future<void> _checkInitialNotificationFromPlatform() async {
    try {
      final result = await _channel.invokeMethod<Map<dynamic, dynamic>>(
        'getInitialNotification',
      );
      if (result != null && result.containsKey('route')) {
        final route = result['route'] as String?;
        if (route != null && route.isNotEmpty) {
          if (kDebugMode) {
            print('App aberto por notificação da plataforma: $route');
          }

          await Future.delayed(const Duration(milliseconds: 1500));
          handleNotificationRoute(route);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print(
          'Erro ao verificar notificação inicial da plataforma: $e',
        );
      }
    }
  }
}

@pragma('vm:entry-point')
void notificationTapBackground(
  NotificationResponse notificationResponse,
) {
  debugPrint(
    'Notificação clicada em segundo plano: ${notificationResponse.payload}',
  );
}
