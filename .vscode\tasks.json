{
    "version": "2.0.0",
    "tasks": [
        // Para executar o comando: Pressione Ctrl+Shift+P e Digite "Run Task"
        // --- Tarefas de Build para Flavors ---
        {
            "label": "Build Dev Profile",
            "type": "shell",
            "command": "flutter build apk --profile -t lib/flavors/main_dev.dart --flavor dev --dart-define-from-file=.envDesenvolvimento",
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "Build Dev Release",
            "type": "shell",
            "command": "flutter build apk --release -t lib/flavors/main_dev.dart --flavor dev --dart-define-from-file=.envDesenvolvimento",
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "Build Prod Profile",
            "type": "shell",
            "command": "flutter build apk --profile -t lib/flavors/main_prod.dart --flavor prod --dart-define-from-file=.env",
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "Build Prod Release",
            "type": "shell",
            "command": "flutter build apk --release -t lib/flavors/main_prod.dart --flavor prod --dart-define-from-file=.env",
            "group": {
                "kind": "build",
                "isDefault": true // Define este como o build padrão ao rodar "Run Build Task"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "Build Prod AppBundle Release",
            "type": "shell",
            "command": "flutter build appbundle --release --obfuscate --split-debug-info=build/symbols -t lib/flavors/main_prod.dart --flavor prod --dart-define-from-file=.env",
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "Run Dev Profile",
            "type": "shell",
            "command": "flutter run --profile -t lib/flavors/main_dev.dart --flavor dev --dart-define-from-file=.envDesenvolvimento",
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "Run Dev Release",
            "type": "shell",
            "command": "flutter run --release -t lib/flavors/main_dev.dart --flavor dev --dart-define-from-file=.envDesenvolvimento",
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "Run Prod Profile",
            "type": "shell",
            "command": "flutter run --profile -t lib/flavors/main_prod.dart --flavor prod --dart-define-from-file=.env",
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "Run Prod Release",
            "type": "shell",
            "command": "flutter run --release -t lib/flavors/main_prod.dart --flavor prod --dart-define-from-file=.env",
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "Stop Profile",
            "type": "shell",
            "command": "pkill -f \"flutter run --profile\"",
            "presentation": {
                "reveal": "always"
            },
            "problemMatcher": []
        },
        {
            "label": "Stop Release",
            "type": "shell",
            "command": "pkill -f \"flutter run --release\"",
            "presentation": {
                "reveal": "always"
            },
            "problemMatcher": []
        }
    ]
}