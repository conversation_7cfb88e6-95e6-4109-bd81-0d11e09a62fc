import 'package:flutter_modular/flutter_modular.dart';

import '../../../supa_base_config_keys.dart';
import '../../services/digital_ocean/config/digital_ocean_config.dart';
import '../../services/digital_ocean/connection/i_storage_connection.dart';
import '../../services/digital_ocean/connection/storage_connection.dart';
import '../../services/supabase/connection/connection.dart';
import '../../services/supabase/connection/i_connection.dart';
import '../../services/supabase/db/db.dart';

class CoreModule extends Module {
  @override
  void exportedBinds(i) {
    i.addSingleton<IConnection>(Connection.new);
    i.addSingleton<DB>(DB.new);
    i.addSingleton<IStorageConnection>(
      () => StorageConnection(config: DigitalOceanConfig.fromEnv()),
    );
    i.addSingleton<SupaBaseConfigKeys>(
      () => SupaBaseConfigKeys(
        apiUrl: const String.fromEnvironment('API_URL'),
        apiAnonKey: const String.fromEnvironment('API_ANON_KEY'),
        apiServiceRoles: const String.fromEnvironment('API_SERVICE_ROLES'),
        googleWebClientId: const String.fromEnvironment('GOOGLE_WEB_CLIENT_ID'),
        androidClientId: const String.fromEnvironment('ANDROID_CLIENT_ID'),
        iosClientId: const String.fromEnvironment('IOS_CLIENT_ID'),
        gcpProjectId: const String.fromEnvironment('GCP_PROJECT_ID'),
      ),
    );
  }
}
