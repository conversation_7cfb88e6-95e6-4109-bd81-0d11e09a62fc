import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/theme/color_outlet.dart';
import 'package:url_launcher/url_launcher.dart';

class TermsAndPrivacyText extends StatelessWidget {
  const TermsAndPrivacyText({super.key});

  @override
  Widget build(BuildContext context) {
    void launchURL(String url) async {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint("Não foi possível abrir o link: $url");
      }
    }

    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: 'Ao criar sua conta no',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: ColorOutlet.contentSecondary,
              fontFamily: TextPattern().fontFamily,
            ),
          ),
          TextSpan(
            text: ' Promobell, ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: ColorOutlet.contentSecondary,
              fontFamily: TextPattern().fontFamily,
            ),
          ),
          TextSpan(
            text: 'você concorda com os nossos ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: ColorOutlet.contentSecondary,
              fontFamily: TextPattern().fontFamily,
            ),
          ),
          TextSpan(
            text: 'Termos de Uso ',
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                launchURL('https://promobell.com.br/termos');
              },
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: ColorOutlet.contentPrimary,
              fontFamily: TextPattern().fontFamily,
            ),
          ),
          TextSpan(
            text: 'e ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: ColorOutlet.contentSecondary,
              fontFamily: TextPattern().fontFamily,
            ),
          ),
          TextSpan(
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                launchURL('https://promobell.com.br/privacidade');
              },
            text: 'Política de Privacidade',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: ColorOutlet.contentPrimary,
              fontFamily: TextPattern().fontFamily,
            ),
          ),
          TextSpan(
            text: '. ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: ColorOutlet.contentSecondary,
              fontFamily: TextPattern().fontFamily,
            ),
          ),
        ],
      ),
    );
  }
}
