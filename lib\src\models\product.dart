import 'dart:convert';

import 'package:flutter/foundation.dart';

class Product {
  final int id;
  final DateTime criadoEm;
  final String plataforma;
  final String urlAfiliado;
  final String urlImagem;
  final String titulo;
  final String categoria;
  final String subcategoria;
  final String descricao;
  final double precoAtual;
  final double precoAntigo;
  final double precoAlternativo;
  final bool ativo;
  final String cupom;
  final bool menorPreco;
  final bool indicamos;
  final bool frete;
  final bool invalidProduct;
  final bool isStory;
  final int? idCategoria;
  final int? idSubcategoria;
  Product({
    required this.id,
    required this.criadoEm,
    required this.plataforma,
    required this.urlAfiliado,
    required this.urlImagem,
    required this.titulo,
    required this.categoria,
    required this.subcategoria,
    required this.descricao,
    required this.precoAtual,
    required this.precoAntigo,
    required this.precoAlternativo,
    required this.ativo,
    required this.cupom,
    required this.menorPreco,
    required this.indicamos,
    required this.frete,
    required this.invalidProduct,
    required this.isStory,
    this.idCategoria,
    this.idSubcategoria,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'criadoEm': criadoEm.millisecondsSinceEpoch,
      'plataforma': plataforma,
      'urlAfiliado': urlAfiliado,
      'urlImagem': urlImagem,
      'titulo': titulo,
      'categoria': categoria,
      'subcategoria': subcategoria,
      'descricao': descricao,
      'precoAtual': precoAtual,
      'precoAntigo': precoAntigo,
      'precoAlternativo': precoAlternativo,
      'ativo': ativo,
      'cupom': cupom,
      'menorPreco': menorPreco,
      'indicamos': indicamos,
      'frete': frete,
      'invalidProduct': invalidProduct,
      'isStory': isStory,
      'categoria_id': idCategoria,
      'subcategoria_id': idSubcategoria,
    };
  }

  // factory Product.fromMap(Map<String, dynamic> map) {
  //   return Product(
  //     id: map['id'] as int,
  //     criadoEm: _parseDateTime(map['criado_em'].toString()),
  //     plataforma: map['plataforma'] as String,
  //     urlAfiliado: map['url_afiliado'] ?? '',
  //     urlImagem: map['url_imagem'] as String,
  //     titulo: map['titulo'] as String,
  //     categoria: map['categoria'] as String,
  //     subcategoria: map['subcategoria'] as String,
  //     descricao: map['descricao'] as String,
  //     precoAtual:
  //         map['preco_atual'] != null
  //             ? (map['preco_atual'] is int
  //                 ? map['preco_atual'].toDouble()
  //                 : map['preco_atual'])
  //             : 0.0,
  //     precoAntigo:
  //         map['preco_antigo'] != null
  //             ? (map['preco_antigo'] is int
  //                 ? map['preco_antigo'].toDouble()
  //                 : map['preco_antigo'])
  //             : 0.0,
  //     precoAlternativo:
  //         map['preco_alternativo'] != null
  //             ? (map['preco_alternativo'] is int
  //                 ? map['preco_alternativo'].toDouble()
  //                 : map['preco_alternativo'])
  //             : 0.0,
  //     ativo: map['ativo'] as bool,
  //     cupom: map['cupom']?.toString() ?? '',
  //     menorPreco: map['menor_preco'] as bool,
  //     indicamos: map['indicamos'] as bool,
  //     frete: map['frete'] as bool,
  //     invalidProduct: map['invalidProduct'] as bool,
  //     isStory: map['isStory'] as bool,
  //     idCategoria: map['categoria_id'] as int?,
  //     idSubcategoria: map['subcategoria_id'] as int?,
  //   );
  // }

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'] as int,
      criadoEm: _parseDateTime(map['criado_em'].toString()),
      plataforma: map['plataforma']?.toString() ?? '',
      urlAfiliado: map['url_afiliado']?.toString() ?? '',
      urlImagem: map['url_imagem']?.toString() ?? '',
      titulo: map['titulo']?.toString() ?? '',
      categoria: map['categoria']?.toString() ?? '',
      subcategoria: map['subcategoria']?.toString() ?? '',
      descricao: map['descricao']?.toString() ?? '',
      precoAtual: _parseDouble(map['preco_atual']),
      precoAntigo: _parseDouble(map['preco_antigo']),
      precoAlternativo: _parseDouble(map['preco_alternativo']),
      ativo: map['ativo'] == true,
      cupom: map['cupom']?.toString() ?? '',
      menorPreco: map['menor_preco'] == true,
      indicamos: map['indicamos'] == true,
      frete: map['frete'] == true,
      invalidProduct:
          map['invalid_product'] == true || map['invalidProduct'] == true,
      isStory: map['is_story'] == true || map['isStory'] == true,
      idCategoria: map['categoria_id'] != null
          ? map['categoria_id'] as int
          : null,
      idSubcategoria: map['subcategoria_id'] != null
          ? map['subcategoria_id'] as int
          : null,
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is int) return value.toDouble();
    if (value is double) return value;
    if (value is String) {
      return double.tryParse(value.replaceAll(',', '.')) ?? 0.0;
    }
    return 0.0;
  }

  static DateTime _parseDateTime(String dateTimeString) {
    try {
      return DateTime.parse(dateTimeString);
    } catch (e) {
      if (kDebugMode) {
        print("Erro ao converter data e hora: $e");
      }
      return DateTime.now();
    }
  }

  String toJson() => json.encode(toMap());

  factory Product.fromJson(String source) =>
      Product.fromMap(json.decode(source) as Map<String, dynamic>);

  factory Product.empty() {
    return Product(
      id: 0,
      criadoEm: DateTime.now(),
      plataforma: '',
      urlAfiliado: '',
      urlImagem: '',
      titulo: '',
      categoria: '',
      subcategoria: '',
      descricao: '',
      precoAtual: 0.0,
      precoAntigo: 0.0,
      precoAlternativo: 0.0,
      ativo: false,
      cupom: '',
      menorPreco: false,
      indicamos: false,
      frete: false,
      invalidProduct: false,
      isStory: false,
      idCategoria: null,
      idSubcategoria: null,
    );
  }

  factory Product.mock() {
    return Product(
      id: 1,
      criadoEm: DateTime.now(),
      plataforma: 'Mercado Livre',
      urlAfiliado:
          'https://www.mercadolivre.com.br/social/promobelloficial/lists/9addf712-7714-49ac-8843-724bcb5f2938?matt_tool=78090322&forceInApp=true',
      urlImagem: 'https://promobell.com.br/favicon.png',
      titulo: 'Produto Promobell',
      categoria: 'Ofertas',
      subcategoria: 'Promoções',
      descricao:
          'O app alerta, você economiza! Baixe o Promobell e receba alertas de ofertas.',
      precoAtual: 0.0,
      precoAntigo: 0.0,
      precoAlternativo: 0.0,
      ativo: true,
      cupom: '',
      menorPreco: false,
      indicamos: true,
      frete: false,
      invalidProduct: false,
      isStory: false,
      idCategoria: 1,
      idSubcategoria: 1,
    );
  }
}
