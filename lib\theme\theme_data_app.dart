import 'package:flutter/material.dart';

import 'color_outlet.dart';
import 'line_height.dart';

class ThemeDataApp {
  get lightTheme => ThemeData(
    useMaterial3: true,
    scaffoldBackgroundColor: ColorOutlet.surface,
    splashColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
    highlightColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
    splashFactory: InkRipple.splashFactory,
    appBarTheme: AppBarTheme(
      iconTheme: IconThemeData(color: ColorOutlet.contentDisabled),
      actionsIconTheme: IconThemeData(color: ColorOutlet.contentDisabled),
    ),

    inputDecorationTheme: InputDecorationTheme(
      hintStyle: TextStyle(
        fontSize: 16,
        fontFamily: 'Figtree',
        color: ColorOutlet.contentGhost,
        height: LineHeight.lh24f16,
      ),
    ),

    textTheme: TextTheme(
      bodyMedium: TextStyle(
        fontSize: 14,
        fontFamily: 'Figtree',
        color: ColorOutlet.contentSecondary,
        fontWeight: FontWeight.w500,
        height: LineHeight.lh20f14,
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        overlayColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
        backgroundColor: ColorOutlet.contentPrimary,
        shadowColor: Colors.transparent,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        textStyle: TextStyle(
          fontSize: 16,
          fontFamily: 'Figtree',
          fontWeight: FontWeight.w600,
          height: LineHeight.lh24f16,
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        overlayColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
        elevation: 0,
        foregroundColor: ColorOutlet.contentTertiary,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    ),
    iconButtonTheme: IconButtonThemeData(
      style: IconButton.styleFrom(
        overlayColor: ColorOutlet.systemBorderDisabled,
        hoverColor: Colors.transparent,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        overlayColor: ColorOutlet.systemBorderDisabled,
        padding: EdgeInsets.symmetric(horizontal: 16),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    ),
    tabBarTheme: TabBarThemeData(
      labelColor: ColorOutlet.contentPrimary,
      unselectedLabelColor: ColorOutlet.contentSecondary,
      labelStyle: TextStyle(
        fontSize: 16,
        fontFamily: 'Figtree',
        fontWeight: FontWeight.w600,
        height: LineHeight.lh24f16,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: 16,
        fontFamily: 'Figtree',
        fontWeight: FontWeight.w500,
        height: LineHeight.lh24f16,
      ),
    ),
    colorScheme: const ColorScheme(
      primary: ColorOutlet.contentPrimary,
      secondary: ColorOutlet.contentSecondary,
      tertiary: ColorOutlet.contentTertiary,
      surface: ColorOutlet.surface,
      error: ColorOutlet.feedbackError,
      onPrimary: ColorOutlet.contentPrimary,
      onSecondary: ColorOutlet.contentSecondary,
      onSurface: ColorOutlet.surface,
      onError: ColorOutlet.feedbackError,
      brightness: Brightness.light,
    ),
    checkboxTheme: CheckboxThemeData(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return ColorOutlet.contentPrimary;
        }
        return ColorOutlet.contentTertiary;
      }),
      checkColor: WidgetStateProperty.all(ColorOutlet.contentTertiary),
      side: WidgetStateBorderSide.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return const BorderSide(
            color: ColorOutlet.contentPrimary,
            width: 1.5,
          );
        }
        return const BorderSide(color: ColorOutlet.contentGhost, width: 1.5);
      }),
    ),
  );

  get darkTheme => ThemeData();
}
