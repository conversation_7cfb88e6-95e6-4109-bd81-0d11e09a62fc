import 'package:flutter/material.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../models/story_model.dart';
import '../../../controllers/offers_controller.dart';
import '../../../controllers/products_details_controller.dart';
import '../../../controllers/story/story_controller.dart';
import '../product_details/product_content.dart';

class CardProducts extends StatelessWidget {
  final StoryModel story;
  final OffersController controller;
  final ProductDetailsController productDetailsController;
  final StoryController storyController;

  const CardProducts({
    required this.story,
    required this.controller,
    required this.storyController,
    required this.productDetailsController,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height:
          story.cupom.trim().isEmpty ||
              story.cupom.toLowerCase().trim() == 'Sem cupom'
          ? 516
          : 594,
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: ColorOutlet.paper,
        borderRadius: BorderRadius.circular(24),
      ),
      child: ProductContent(
        product: storyController.converterStoryParaPoduct(story),
        controller: controller,
        productDetailsController: productDetailsController,
        isStory: true,
      ),
    );
  }
}
