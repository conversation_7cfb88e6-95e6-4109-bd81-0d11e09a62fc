import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/base/controllers/base_controller/base_controller.dart';
import '../../../../models/categorias_menu.dart';
import '../../controllers/offers_controller.dart';
import '../../controllers/products_details_controller.dart';
import '../../controllers/story/story_animation_controller.dart';
import '../../controllers/story/story_controller.dart';
import '../../controllers/story/story_navigation_controller.dart';
import '../widget/storys/story_background.dart';
import '../widget/storys/story_content_view.dart';

class StoryViewScreen extends StatefulWidget {
  final List<String> orderedCategories;
  final String initialCategory;
  final CategoriaMenu category;
  final int initialIndex;
  final Function(int) onStoryComplete;

  const StoryViewScreen({
    super.key,
    required this.orderedCategories,
    required this.initialCategory,
    required this.initialIndex,
    required this.category,
    required this.onStoryComplete,
  });

  @override
  StoryViewScreenState createState() => StoryViewScreenState();
}

class StoryViewScreenState extends State<StoryViewScreen> with TickerProviderStateMixin {
  final productDetailsController = Modular.get<ProductDetailsController>();
  final controllerBase = Modular.get<BaseController>();
  final storyController = Modular.get<StoryController>();
  final storyAnimationController = Modular.get<StoryAnimationController>();
  final controller = Modular.get<OffersController>();
  late final StoryNavigationController navigationController;
  int followers = 0;

  @override
  void initState() {
    super.initState();

    navigationController = StoryNavigationController(
      storyController: storyController,
      animationController: storyAnimationController,
      controllerBase: controllerBase,
    );

    navigationController.initializeControllers(
      initialCategory: widget.initialCategory,
      orderedCategories: widget.orderedCategories,
      initialIndex: widget.initialIndex,
    );

    storyAnimationController.initializeTimeline(this);

    storyAnimationController.addListener(() {
      if (storyAnimationController.progress >= 1.0 && !storyAnimationController.isLongPressed) {
        navigationController.nextStory(markAsViewed: true);
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      markCurrentStoryAsViewed();
    });

    controller.fetchFollowersCount(widget.category.id);
    controller.fetchFollowStatus(
      widget.category.id,
      Supabase.instance.client.auth.currentUser?.email ?? '',
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        storyAnimationController.startAnimation();
      }
    });
  }

  void markCurrentStoryAsViewed() {
    final currentStory = navigationController.currentStory;
    if (currentStory != null) {
      widget.onStoryComplete(currentStory.id);
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (storyAnimationController.isLongPressed) return;
    if (!navigationController.canNavigate) return;

    final screenWidth = MediaQuery.of(context).size.width;
    final isLeftSide = details.globalPosition.dx < screenWidth / 2;

    navigationController.handleTapNavigation(isLeftSide);
    storyAnimationController.resumeAnimation();
  }

  void _handleLongPressStart(LongPressStartDetails details) {
    storyAnimationController.setLongPress(true);
    storyAnimationController.pauseAnimation();
  }

  void _handleLongPressEnd(LongPressEndDetails details) {
    storyAnimationController.setLongPress(false);
    storyAnimationController.resumeAnimation();
  }

  Future<void> animateAndNavigateBack() async {
    if (mounted) {
      storyController.savePosition(
        navigationController.currentCategory,
        navigationController.currentIndex,
        navigationController.currentStories.length,
      );
      storyAnimationController.setTransitioning(true);
      storyController.reorderCategories();
      storyAnimationController.setPageLoadState(false);
      markCurrentStoryAsViewed();
      Modular.to.navigate(
        '/home',
        arguments: {
          'maintainIndex': true,
          'tapPosition': storyController.tapPosition,
        },
      );
      controllerBase.navPage(0);
    }
  }

  @override
  void dispose() {
    storyController.savePosition(
      navigationController.currentCategory,
      navigationController.currentIndex,
      navigationController.currentStories.length,
    );
    navigationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final topPadding = MediaQuery.of(context).padding.top;

    return AnimatedBuilder(
      animation: Listenable.merge([
        controller,
        storyController,
        storyAnimationController,
        navigationController.categoryPageController,
        navigationController.storyPageController,
        navigationController,
      ]),
      builder: (context, _) {
        if (widget.orderedCategories.isEmpty) {
          return const SizedBox();
        }

        final currentStory = navigationController.currentStory;
        if (currentStory == null) {
          return const Center(child: CircularProgressIndicator());
        }

        return Scaffold(
          body: Stack(
            children: [
              StoryBackground(
                storyAnimationController: storyAnimationController,
                currentStory: currentStory,
              ),
              PageView.builder(
                controller: navigationController.categoryPageController,
                onPageChanged: (index) {
                  if (index >= 0 && index < widget.orderedCategories.length) {
                    final categoryName = widget.orderedCategories[index];
                    final categoria = CategoriaMenu.getCategoriaByNome(
                      categoryName,
                    );

                    navigationController.currentCategory = categoryName;
                    controller.fetchFollowersCount(categoria.id);
                    controller.fetchFollowStatus(
                      categoria.id,
                      Supabase.instance.client.auth.currentUser?.email ?? '',
                    );
                  }
                },
                itemCount: widget.orderedCategories.length,
                itemBuilder: (context, categoryIndex) {
                  final categoryName = widget.orderedCategories[categoryIndex];
                  final categoria = CategoriaMenu.getCategoriaByNome(
                    categoryName,
                  );
                  final stories = storyController.getStoriesByCategory(
                    categoryName,
                  );

                  if (stories.isEmpty || categoryIndex >= widget.orderedCategories.length) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  return GestureDetector(
                    onTapUp: _handleTapUp,
                    onLongPressStart: _handleLongPressStart,
                    onLongPressEnd: _handleLongPressEnd,
                    onVerticalDragStart: (details) {
                      storyAnimationController.handleDragStart(
                        details.globalPosition.dy,
                      );
                    },
                    onVerticalDragUpdate: (details) {
                      storyAnimationController.handleDragUpdate(
                        details.globalPosition.dy,
                      );
                    },
                    onVerticalDragEnd: (details) {
                      storyAnimationController.handleDragEnd();
                      if (storyAnimationController.dragDistance > storyAnimationController.dragThreshold) {
                        animateAndNavigateBack();
                      } else {
                        storyAnimationController.resetDrag();
                      }
                    },
                    child: StoryContentView(
                      navigationController: navigationController,
                      storyAnimationController: storyAnimationController,
                      stories: stories,
                      topPadding: topPadding,
                      productDetailsController: productDetailsController,
                      controller: controller,
                      storyController: storyController,
                      controllerBase: controllerBase,
                      categoria: categoria,
                      onClose: animateAndNavigateBack,
                      product: storyController.converterStoryParaPoduct(
                        currentStory,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}

Color filtroCor(Color cor, Color filtro) {
  return Color.alphaBlend(filtro, cor);
}
