import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell/src/modules/offers/ui/widget/offers_page/debouncer.dart';
import 'package:promobell/src/services/navigation/scroll_services.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../core/base/controllers/base_controller/base_controller.dart';
import '../../../controllers/offers_controller.dart';

class CampoBuscar extends StatefulWidget {
  final bool isFixedFilter;
  const CampoBuscar({super.key, required this.isFixedFilter});

  @override
  State<CampoBuscar> createState() => _CampoBuscarState();
}

class _CampoBuscarState extends State<CampoBuscar>
    with AutomaticKeepAliveClientMixin {
  final OffersController controller = Modular.get<OffersController>();
  final BaseController baseController = Modular.get<BaseController>();
  final _scrollService = ScrollService();

  bool _isSearching = false;

  late TextEditingController _textController;
  late FocusNode _focusNode;
  late Debouncer _debouncer;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    _textController = TextEditingController();
    _focusNode = FocusNode();
    _debouncer = Debouncer(milliseconds: 700);

    // Inicializar com o valor atual do controller se existir
    if (controller.searchQuery.isNotEmpty) {
      _textController.text = controller.searchQuery;
      _isSearching = true;
    }

    _textController.addListener(_onTextChanged);

    baseController.pageIndexNotifier.addListener(_resetOnPageChange);
    Modular.to.addListener(_resetOnPageChange);
  }

  @override
  void dispose() {
    baseController.pageIndexNotifier.removeListener(
      _resetOnPageChange,
    );
    Modular.to.removeListener(_resetOnPageChange);

    _textController.dispose();
    _focusNode.dispose();
    _debouncer.cancel();

    super.dispose();
  }

  void _onTextChanged() {
    if (!mounted) return;

    final newSearching = _textController.text.isNotEmpty;
    if (_isSearching != newSearching) {
      setState(() {
        _isSearching = newSearching;
      });
    }
  }

  void _resetOnPageChange() {
    if (!Modular.to.path.contains('/offers') && mounted) {
      if (_focusNode.hasFocus) {
        _focusNode.unfocus();
      }
    }
  }

  void _handleSearch(String value) {
    _debouncer.run(() {
      if (!mounted) return;

      controller.limparTodosOsFiltros(isCategoryFilters: false);
      controller.buscarProdutos(value);

      final scrollController = _scrollService.getScrollController(
        'offersPage',
      );

      if (scrollController == null || !scrollController.hasClients) {
        return;
      }

      final offset = scrollController.offset;

      if (_focusNode.hasFocus && offset > 1.0) {
        Future.delayed(const Duration(milliseconds: 100), () {
          if (_focusNode.hasFocus && scrollController.hasClients) {
            scrollController.animateTo(
              0.0,
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeOut,
            );
          }
        });
      }
    });
  }

  void _handleClear() {
    _textController.clear();
    controller.setTipoLista(TipoListaProdutos.geral);
    controller.limparTodosOsFiltros(isCategoryFilters: false);
    controller.buscarProdutos('');

    setState(() {
      _isSearching = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Container(
      width: double.infinity,
      alignment: Alignment.bottomCenter,
      child: Stack(
        alignment: Alignment.center,
        children: [
          if (widget.isFixedFilter)
            Container(
              height: 48,
              decoration: BoxDecoration(
                color: ColorOutlet.surface,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
            ),
          Align(
            alignment: Alignment.topCenter,
            child: Container(
              height: 35,
              decoration: BoxDecoration(color: ColorOutlet.surface),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SizedBox(
              height: 48,
              width: double.infinity,
              child: TextField(
                controller: _textController,
                focusNode: _focusNode,
                textInputAction: TextInputAction.search,
                onSubmitted: _handleSearch,
                onTapOutside: (event) => _focusNode.unfocus(),
                autocorrect: false,
                textCapitalization: TextCapitalization.none,
                enableSuggestions: true,
                decoration: InputDecoration(
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                  hintText: 'produtos, marcas e muito mais...',
                  hintStyle: TextStyle(
                    color: ColorOutlet.contentGhost,
                    fontSize: 14,
                    fontFamily: TextPattern().fontFamily,
                    fontWeight: FontWeight.w400,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: ColorOutlet.paper,
                  prefixIcon: _isSearching || _textController.text.isNotEmpty
                      ? Padding(
                          padding: EdgeInsets.only(
                            left: 6,
                            right: 8,
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(6.0),
                            child: SvgPicture.asset(
                              SvgIcons.actionSearchIaBold,
                            ),
                          ),
                        )
                      : Padding(
                          padding: EdgeInsets.only(
                            left: 12,
                            right: 8,
                          ),
                          child: SvgPicture.asset(
                            SvgIcons.encontreAiColorAll,
                          ),
                        ),
                  suffixIcon: _isSearching || _textController.text.isNotEmpty
                      ? IconButton(
                          icon: SvgPicture.asset(
                            SvgIcons.actionCloseSmall,
                            height: 24,
                            width: 24,
                          ),
                          color: ColorOutlet.contentSecondary,
                          onPressed: _handleClear,
                        )
                      : null,
                ),
                cursorColor: ColorOutlet.contentSecondary,
                style: TextStyle(
                  color: ColorOutlet.contentSecondary,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
