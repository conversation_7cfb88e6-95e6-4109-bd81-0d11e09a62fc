import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../../../../theme/color_outlet.dart';
import '../../../../../../../theme/svg_icons.dart';
import '../../../../../services/logs/app_logger.dart';

class BlurEffectImage extends StatelessWidget {
  final String? image;
  final Uint8List? tempImage;

  const BlurEffectImage({required this.image, this.tempImage, super.key});

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: Align(
        alignment: Alignment.center,
        heightFactor: 0.3,
        child: ImageFiltered(
          imageFilter: ImageFilter.blur(sigmaX: 24, sigmaY: 24),
          child: Container(
            height: 510,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: ColorOutlet.surface, width: 50),
              ),
            ),
            child: tempImage != null
                ? Image.memory(tempImage!, fit: BoxFit.fitHeight)
                : image?.isNotEmpty == true
                ? Image.network(
                    image!,
                    fit: BoxFit.fitHeight,
                    errorBuilder: (context, error, stackTrace) {
                      AppLogger.logError(
                        'Erro ao carregar imagem de fundo',
                        error,
                        stackTrace,
                      );
                      return Image.network(
                        'https://i.imgur.com/CqtNBvb.png',
                        fit: BoxFit.fitHeight,
                        errorBuilder: (context, error, stackTrace) =>
                            _buildDefaultBackground(),
                      );
                    },
                  )
                : _buildDefaultBackground(),
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultBackground() {
    return SizedBox(
      width: 24,
      height: 24,
      child: FittedBox(
        // Garante que o SVG se ajuste corretamente ao tamanho desejado
        child: SvgPicture.asset(SvgIcons.archiveImageBroken, fit: BoxFit.cover),
      ),
    );
  }
}
