import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/src/modules/offers/ui/widget/att_page/update_app_button.dart';
import 'package:promobell/src/modules/offers/ui/widget/att_page/update_consent_text.dart';

import '../../../../../theme/color_outlet.dart';
import '../../../../models/categorias_menu.dart';
import '../../../categories/ui/widgets/category_card.dart';
import '../../controllers/offers_controller.dart';

class AttPage extends StatelessWidget {
  final bool isUpdateForce;
  const AttPage({super.key, required this.isUpdateForce});

  @override
  Widget build(BuildContext context) {
    const String text = '+1000 ofertas';
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final topPadding = MediaQuery.of(context).padding.top;
    final screenHeight = MediaQuery.of(context).size.height;

    OffersController controller = Modular.get<OffersController>();

    return Scaffold(
      backgroundColor: ColorOutlet.contentPrimary,
      body: SingleChildScrollView(
        child: ConstrainedBox(
          constraints: BoxConstraints(minHeight: screenHeight),
          child: IntrinsicHeight(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    right: 16,
                    left: 16,
                    top: topPadding + 16,
                    bottom: 78,
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                top: 60,
                                bottom: 44,
                                left: 8,
                              ),
                              child: TextPattern.customText(
                                text:
                                    'Tem uma nova versão do app disponível ...',
                                fontWeightOption: FontWeightOption.bold,
                                fontSize: 20,
                                color: ColorOutlet.contentTertiary,
                              ),
                            ),
                            CategoryCard(
                              categoria: CategoriaMenu.categorias[1],
                              width: double.infinity,
                              height: 199,
                              numberOfOffers: text,
                              maxWidth: double.infinity,
                              image: CategoriaMenu.categorias[1].fotoPequena,
                            ),
                            SizedBox(height: 16),
                            CategoryCard(
                              categoria: CategoriaMenu.categorias[3],
                              width: double.infinity,
                              height: 144,
                              numberOfOffers: text,
                              maxWidth: double.infinity,
                              image: CategoriaMenu.categorias[3].fotoPequena,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          children: [
                            CategoryCard(
                              categoria: CategoriaMenu.categorias[0],
                              width: double.infinity,
                              height: 199,
                              numberOfOffers: text,
                              maxWidth: double.infinity,
                              image: CategoriaMenu.categorias[0].fotoPequena,
                            ),
                            SizedBox(height: 16),
                            CategoryCard(
                              categoria: CategoriaMenu.categorias[2],
                              width: double.infinity,
                              height: 144,
                              numberOfOffers: text,
                              maxWidth: double.infinity,
                              image: CategoriaMenu.categorias[2].fotoPequena,
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                top: 54,
                                left: 8,
                              ),
                              child: TextPattern.customText(
                                text: '... com melhorias e novidades pra você.',
                                fontWeightOption: FontWeightOption.bold,
                                fontSize: 20,
                                color: ColorOutlet.contentTertiary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const UpdateConsentText(),
                Padding(
                  padding: EdgeInsets.only(
                    bottom: bottomPadding + 32,
                    left: 32,
                    right: 32,
                    top: 40,
                  ),
                  child: Visibility(
                    visible: isUpdateForce,
                    replacement: const UpdateAppButton(),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: TextButton(
                            onPressed: () {
                              controller.setValuePrecisaAtualizar(
                                false,
                              );
                              Modular.to.navigate('/home');
                            },
                            child: TextPattern.customText(
                              text: 'Depois',
                              color: ColorOutlet.surface,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          flex: 2,
                          child: UpdateAppButton(),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
