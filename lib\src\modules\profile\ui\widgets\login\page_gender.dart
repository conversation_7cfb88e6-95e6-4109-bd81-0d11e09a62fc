import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/components/custom_snack_bar.dart';
import 'package:promobell/src/modules/categories/ui/widgets/detail/custom_dialog.dart';
import 'package:promobell/src/modules/profile/controllers/profile_controller.dart';
import 'package:promobell/src/modules/profile/ui/widgets/close_button_onboarding.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/custom_radio_tile.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/navigation_buttons_row.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_view_template.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/progress_badge.dart';
import 'package:promobell/theme/svg_icons.dart';
import 'onboarding_title_block.dart';

class PageGender extends StatefulWidget {
  final VoidCallback onBack;
  final ProfileController controller;

  const PageGender({
    required this.onBack,
    super.key,
    required this.controller,
  });

  @override
  State<PageGender> createState() => _PageGenderState();
}

class _PageGenderState extends State<PageGender> {
  final List<String> generos = [
    'Como ele',
    'Como ela',
    'De forma neutra (sem usar ele/ela)',
  ];

  @override
  Widget build(BuildContext context) {
    final paddingBottom = MediaQuery.of(context).padding.bottom;
    return PageViewTemplate(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ProgressBadge(text: '3/3'),
              CloseButtonOnboarding(
                onPressed: () => _showWarningDialog(context),
              ),
            ],
          ),
          OnboardingTitleBlock(
            title:
                'Perfeito!\nE como você gostaria que a gente se referisse a você?',
            subtitle:
                'Assim, a gente usa os pronomes certos e fala com você do seu jeito.',
          ),
          const SizedBox(height: 32),
          Column(
            spacing: 16,
            children: [
              ...generos.map((genero) {
                return CustomRadioTile(
                  value: genero,
                  groupValue: widget.controller.gender,
                  onChanged: (value) {
                    setState(() {
                      widget.controller.gender = value;
                    });
                  },
                  text: genero,
                );
              }),
            ],
          ),
          const Spacer(),
          NavigationButtonsRow(
            onBack: widget.onBack,
            text: 'Começar a economizar',
            onNext: () {
              // Validação: só prossegue se um gênero foi selecionado
              if (!widget.controller.isGenderSelected) {
                // Aqui você pode mostrar um snackbar ou dialog de erro
                CustomSnackBar.show(
                  context: context,
                  message: 'Por favor, selecione uma opção antes de continuar.',
                  icon: SvgIcons.feedbackInfo,
                );

                return;
              }

              // Se chegou até aqui, gênero foi selecionado
              final success = widget.controller.setGender();
              if (success) {
                widget.controller.updadeDataUser();
                Modular.to.navigate('/home');
              } else {
                // Erro inesperado
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'Erro ao salvar seleção. Tente novamente.',
                    ),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
          SizedBox(height: paddingBottom),
        ],
      ),
    );
  }

  void _showWarningDialog(BuildContext context) {
    CustomDialog.show(
      context,
      title: 'Completar perfil depois',
      message:
          'Ao sair, as informações preenchidas serão descartadas. \n\nVocê poderá completá-las mais tarde nas configurações do perfil.',
      onConfirm: () {},
      onCancel: () {
        Modular.to.navigate('/home');
      },
      buttonOnly: false,
      textOnConfirm: 'Continuar editando',
      textOnCancel: 'Sair',
    );
  }
}
