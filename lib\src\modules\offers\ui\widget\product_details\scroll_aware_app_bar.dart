import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/components/custom_snack_bar.dart';

import '../../../../../../app_links_service.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../core/base/controllers/base_controller/base_controller.dart';
import '../../../../categories/ui/widgets/detail/button_icon_with_background.dart';
import '../../../controllers/offers_controller.dart';
import '../../../controllers/products_details_controller.dart';
import '../../pages/product_details_page.dart';

class ScrollAwareAppBar extends StatefulWidget {
  final ProductDetailsPage widget;
  final ProductDetailsController productDetailsController;
  const ScrollAwareAppBar({
    super.key,
    required this.widget,
    required this.productDetailsController,
  });

  @override
  State<ScrollAwareAppBar> createState() => _ScrollAwareAppBarState();
}

class _ScrollAwareAppBarState extends State<ScrollAwareAppBar> {
  @override
  Widget build(BuildContext context) {
    final BaseController controllerBase = Modular.get<BaseController>();
    final OffersController controller = Modular.get<OffersController>();

    return Column(
      children: [
        SizedBox(height: widget.productDetailsController.topPadding),
        SizedBox(
          height: 56,
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ButtonIconWithBackground(
                      iconPath: SvgIcons.arrowClearLeft,
                      onPressed: () {
                        if (AppLinksService.hasReceivedLink) {
                          controllerBase.navBackFromProductDeepLink();
                        } else {
                          // Comportamento normal
                          controllerBase.navPage(0);
                          Modular.to.pop();
                        }
                      },
                    ),
                    SizedBox(width: 10),
                    Container(
                      height: 40,
                      alignment: Alignment.center,
                      child: TextPattern.customText(
                        text: 'Oferta',
                        fontSize: 20,
                        fontWeightOption: FontWeightOption.bold,
                      ),
                    ),
                    Spacer(),
                    ButtonIconWithBackground(
                      iconPath:
                          controller.isProductSavedSync(
                            widget.widget.product,
                          )
                          ? SvgIcons.markerBookmarkFilled
                          : SvgIcons.markerBookmark,
                      isSared: controller.isLoadingShare,
                      onPressed: () {
                        controller.toggleSavedProduct(
                          widget.widget.product,
                        );
                        CustomSnackBar.show(
                          context: context,
                          noBottomPadding: true,
                          message: "Produto salvo na sua lista de ofertas",
                          icon: SvgIcons.feedbackCheck,
                        );
                      },
                    ),
                    SizedBox(width: 8),
                    ButtonIconWithBackground(
                      iconPath: SvgIcons.actionShare,
                      isSared: controller.isLoadingShare,
                      onPressed: () => controller.launchWhatsApp(
                        widget.widget.product,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
