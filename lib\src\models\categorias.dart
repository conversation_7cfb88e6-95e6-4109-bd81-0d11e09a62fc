import 'package:promobell/src/models/subcategoria.dart';

class Categoria {
  final int id;
  final String nome;
  final List<Subcategoria> subcategorias;

  Categoria({
    required this.id,
    required this.nome,
    required this.subcategorias,
  });

  factory Categoria.fromJson(
    Map<String, dynamic> json,
    List<Subcategoria> allSubcategorias,
  ) {
    return Categoria(
      id: json['id'],
      nome: json['nome'],
      subcategorias: allSubcategorias
          .where((sub) => sub.idCategoria == json['id'])
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'subcategorias': subcategorias.map((sub) => sub.toJson()).toList(),
    };
  }
}
