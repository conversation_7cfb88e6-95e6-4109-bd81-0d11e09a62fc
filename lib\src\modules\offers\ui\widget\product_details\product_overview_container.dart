import 'package:flutter/material.dart';
import 'package:promobell/src/modules/offers/ui/widget/product_details/product_content.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../models/product.dart';
import '../../../controllers/offers_controller.dart';
import '../../../controllers/products_details_controller.dart';

class ProductOverviewContainer extends StatefulWidget {
  final OffersController controller;
  final ProductDetailsController productDetailsController;
  final Product product;

  const ProductOverviewContainer({
    super.key,
    required this.controller,
    required this.product,
    required this.productDetailsController,
  });

  @override
  State<ProductOverviewContainer> createState() =>
      _ProductOverviewContainerState();
}

class _ProductOverviewContainerState extends State<ProductOverviewContainer>
    with SingleTickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: widget.productDetailsController.totalAppBarHeight,
      ),
      child: SingleChildScrollView(
        physics: const NeverScrollableScrollPhysics(),
        child: SizedBox(
          height:
              widget.product.cupom.trim().isEmpty ||
                  widget.product.cupom.toLowerCase().trim() == 'sem cupom'
              ? 516
              : 602,
          child: Container(
            padding: EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: ColorOutlet.paper,
              borderRadius: BorderRadius.circular(24),
            ),
            child: AnimatedOpacity(
              opacity: widget.productDetailsController.opacityCard,
              duration: const Duration(milliseconds: 100),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 100),
                transform: Matrix4.translationValues(
                  0,
                  widget.productDetailsController.columnTranslateY,
                  0,
                ),
                child: ProductContent(
                  product: widget.product,
                  controller: widget.controller,
                  productDetailsController: widget.productDetailsController,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
