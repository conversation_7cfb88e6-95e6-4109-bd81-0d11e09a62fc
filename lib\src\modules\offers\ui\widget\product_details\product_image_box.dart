import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../models/product.dart';

class ProductImageBox extends StatelessWidget {
  final double height;
  final double width;
  final double radius;
  final Product product;
  final BoxFit? fit;

  const ProductImageBox({
    super.key,
    this.height = 216,
    this.width = double.infinity,
    this.radius = 16,
    required this.product,
    this.fit = BoxFit.cover,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: ColorOutlet.surface,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(radius),

        child: CachedNetworkImage(
          imageUrl: product.urlImagem,
          height: height,
          width: width,
          fit: BoxFit.contain,
          color: ColorOutlet.surface,
          colorBlendMode: BlendMode.multiply,
          progressIndicatorBuilder: (context, url, progress) => Center(
            child: CircularProgressIndicator(
              value: progress.progress,
              color: ColorOutlet.contentPrimary,
              strokeWidth: 1.5,
            ),
          ),
          errorWidget: (context, url, error) => Center(
            child: SizedBox(
              width: 24,
              height: 24,
              child: FittedBox(
                child: SvgPicture.asset(
                  SvgIcons.archiveImageBroken,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
