import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';

class OutlinedShareButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool shared;

  const OutlinedShareButton({
    required this.onPressed,
    required this.shared,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(
            color: ColorOutlet.feedbackDisabled,
            width: 1,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          padding: EdgeInsets.symmetric(horizontal: 16),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              SvgIcons.actionShare,
              height: 20,
              width: 20,
            ),
            SizedBox(width: 8),
            Flexible(
              child: shared
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 1.5,
                      ),
                    )
                  : TextPattern.customText(
                      text: 'Compartilhar',
                      fontSize: 14,
                      overflow: TextOverflow.ellipsis,
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
