import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/modules/categories/ui/widgets/detail/custom_dialog.dart';
import 'package:promobell/src/modules/profile/controllers/profile_controller.dart';
import 'package:promobell/src/modules/profile/ui/widgets/close_button_onboarding.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/navigation_buttons_row.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_view_template.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/progress_badge.dart';

import '../../../../../components/custom_snack_bar.dart';
import 'custom_text_input.dart';
import 'onboarding_title_block.dart';

class PageAge extends StatefulWidget {
  final VoidCallback onNext;
  final VoidCallback onBack;
  final ProfileController controller;

  const PageAge({
    required this.controller,
    required this.onNext,
    required this.onBack,
    super.key,
  });

  @override
  State<PageAge> createState() => _PageAgeState();
}

class _PageAgeState extends State<PageAge> {
  late TextEditingController dayController;
  late TextEditingController monthController;
  late TextEditingController yearController;
  final dayFocusController = FocusNode();
  final monthFocusController = FocusNode();
  final yearFocusController = FocusNode();
  final formKey = GlobalKey<FormState>();
  bool _snackBarAberto = false;

  @override
  void initState() {
    dayController = TextEditingController(text: widget.controller.dia)
      ..addListener(_updateControllerValues);
    monthController = TextEditingController(
      text: widget.controller.mes,
    )..addListener(_updateControllerValues);
    yearController = TextEditingController(
      text: widget.controller.ano,
    )..addListener(_updateControllerValues);
    super.initState();
  }

  void _updateControllerValues() {
    widget.controller.setDataNascimento(
      dayController.text,
      monthController.text,
      yearController.text,
    );
  }

  final List<TextInputFormatter> inputFormatters = [
    LengthLimitingTextInputFormatter(2),
    FilteringTextInputFormatter.digitsOnly,
  ];

  void _moveFocusIfNeeded(
    TextEditingController controller,
    FocusNode currentFocusNode,
    FocusNode nextFocusNode,
    int maxLength,
  ) {
    if (controller.text.length == maxLength) {
      FocusScope.of(context).requestFocus(nextFocusNode);
    }
  }

  @override
  void dispose() {
    dayFocusController.dispose();
    monthFocusController.dispose();
    yearFocusController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final paddingBottom = MediaQuery.of(context).padding.bottom;
    return AnimatedBuilder(
      animation: Listenable.merge([
        dayController,
        monthController,
        yearController,
        dayFocusController,
        monthFocusController,
        yearFocusController,
      ]),
      builder: (context, _) {
        return LayoutBuilder(
          builder: (context, constraints) {
            final double totalWidth = constraints.maxWidth;
            final double usableWidth = totalWidth - 48;
            final double spacing = 16;
            final double fieldCount = 3;
            final double fieldWidth =
                (usableWidth - (spacing * (fieldCount - 1))) / fieldCount;

            return PageViewTemplate(
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ProgressBadge(text: '2/3'),
                      CloseButtonOnboarding(
                        onPressed: () => _showWarningDialog(context),
                      ),
                    ],
                  ),
                  OnboardingTitleBlock(
                    title:
                        'Ótimo!\n${(widget.controller.userName?.trim().isNotEmpty ?? false) ? widget.controller.userName : 'Promolover'}, agora conta pra gente sua idade?',
                    subtitle:
                        'Com essa informação, podemos recomendar ofertas adequadas pra você.',
                  ),
                  SizedBox(height: 32),
                  Form(
                    key: formKey,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 16,
                      children: [
                        CustomTextInput(
                          isCenter: true,
                          width: fieldWidth,
                          controller: dayController,
                          keyboardType: TextInputType.number,
                          text: 'Dia',
                          inputFormatters: inputFormatters,
                          validator: widget.controller.validarDia,
                          focusNode: dayFocusController,
                          textInputAction: TextInputAction.next,
                          onChanged: (_) => _moveFocusIfNeeded(
                            dayController,
                            dayFocusController,
                            monthFocusController,
                            2,
                          ),
                        ),
                        CustomTextInput(
                          isCenter: true,
                          width: fieldWidth,
                          controller: monthController,
                          text: 'Mês',
                          keyboardType: TextInputType.number,
                          validator: widget.controller.validarMes,
                          inputFormatters: inputFormatters,
                          focusNode: monthFocusController,
                          textInputAction: TextInputAction.next,
                          onChanged: (_) => _moveFocusIfNeeded(
                            monthController,
                            monthFocusController,
                            yearFocusController,
                            2,
                          ),
                        ),
                        CustomTextInput(
                          isCenter: true,
                          width: fieldWidth,
                          controller: yearController,
                          text: 'Ano',
                          keyboardType: TextInputType.number,
                          validator: widget.controller.validarAno,
                          inputFormatters: [
                            LengthLimitingTextInputFormatter(4),
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                          focusNode: yearFocusController,
                          textInputAction: TextInputAction.done,
                          onFieldSubmitted: (_) =>
                              FocusScope.of(context).unfocus(),
                          onChanged: (value) {
                            if (value.length == 4) {
                              FocusScope.of(context).unfocus();
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                  Spacer(),
                  NavigationButtonsRow(
                    onBack: widget.onBack,
                    onNext: () {
                      final isFormValido = formKey.currentState!.validate();

                      if (!isFormValido) return;

                      final erroData = widget.controller.validarDataCompleta(
                        dayController.text,
                        monthController.text,
                        yearController.text,
                      );

                      if (erroData != null) {
                        if (!_snackBarAberto) {
                          _snackBarAberto = true;
                          CustomSnackBar.show(
                            context: context,
                            message: erroData,
                            noIcon: false,
                            justTheBottom: true,
                          );

                          Future.delayed(
                            const Duration(seconds: 2),
                            () {
                              _snackBarAberto = false;
                            },
                          );
                        }
                        return;
                      }
                      widget.controller.setDataNascimento(
                        dayController.text,
                        monthController.text,
                        yearController.text,
                      );
                      widget.controller.finalizarDataNascimento();
                      widget.onNext();
                    },
                  ),
                  SizedBox(height: paddingBottom),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _showWarningDialog(BuildContext context) {
    CustomDialog.show(
      context,
      title: 'Completar perfil depois',
      message:
          'Ao sair, as informações preenchidas serão descartadas. \n\nVocê poderá completá-las mais tarde nas configurações do perfil.',
      onConfirm: () {},
      onCancel: () {
        Modular.to.navigate('/home');
      },
      buttonOnly: false,
      textOnConfirm: 'Continuar editando',
      textOnCancel: 'Sair',
    );
  }
}
