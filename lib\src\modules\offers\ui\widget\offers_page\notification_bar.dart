import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';

class NotificationBar extends StatelessWidget {
  final void Function()? onTap;
  const NotificationBar({super.key, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(maxHeight: 64),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SvgPicture.asset('assets/icons/offer/logo.svg', width: 164),
          InkWell(
            splashColor: ColorOutlet.systemBorderDisabled.withValues(
              alpha: 0.3,
            ),
            highlightColor: ColorOutlet.systemBorderDisabled.withValues(
              alpha: 0.3,
            ),
            onTap: onTap,
            borderRadius: BorderRadius.circular(16),
            child: Container(
              width: 40,
              height: 40,
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: ColorOutlet.systemSurface,
              ),
              child: SvgPicture.asset(
                SvgIcons.actionNotification,
                width: 24,
                height: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
