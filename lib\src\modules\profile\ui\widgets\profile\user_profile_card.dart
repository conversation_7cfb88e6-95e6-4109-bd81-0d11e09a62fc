import '../../../../../components/text_pattern.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class UserProfileCard extends StatelessWidget {
  final String name;
  final String joinedDate;
  final VoidCallback onPressed;
  final bool isCompletedProfile;

  const UserProfileCard({
    required this.isCompletedProfile,
    required this.onPressed,
    required this.name,
    required this.joinedDate,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 16,
      children: [
        SizedBox(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(width: 16),
              TextPattern.customText(
                text: name,
                fontSize: 20,
                fontWeightOption: FontWeightOption.bold,
              ),
              SizedBox(width: 4),
              SvgPicture.asset(
                SvgIcons.markerVerifiedFilled,
                colorFilter: ColorFilter.mode(
                  ColorOutlet.contentPrimary,
                  BlendMode.srcIn,
                ),
                height: 20,
                width: 20,
              ),
            ],
          ),
        ),
        TextPattern.customText(
          text: 'Economizando desde $joinedDate',
          fontSize: 12,
          color: ColorOutlet.contentGhost,
        ),
        Visibility(
          visible: isCompletedProfile,
          replacement: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              color: ColorOutlet.surface,
            ),
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  Row(
                    children: [
                      SvgPicture.asset(
                        SvgIcons.feedbackCautionFilled,
                        height: 24,
                        width: 24,
                      ),
                      SizedBox(width: 4),
                      TextPattern.customText(
                        text: 'Seu perfil tá quase lá!',
                        fontSize: 16,
                        fontWeightOption: FontWeightOption.semiBold,
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  TextPattern.customText(
                    text:
                        'Complete seu perfil rapidinho pra deixar tudo com a sua cara enquanto usa o app.',
                    fontSize: 12,
                    color: ColorOutlet.contentGhost,
                  ),
                  SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: onPressed,
                      child: TextPattern.customText(
                        text: 'Completar perfil',
                        color: ColorOutlet.contentTertiary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          child: TextButton(
            onPressed: onPressed,
            child: TextPattern.customText(
              text: 'Editar conta',
              color: ColorOutlet.contentPrimary,
            ),
          ),
        ),
      ],
    );
  }
}
