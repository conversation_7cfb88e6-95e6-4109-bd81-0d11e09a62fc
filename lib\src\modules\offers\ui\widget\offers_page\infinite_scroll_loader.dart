import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class InfiniteScrollLoader extends StatelessWidget {
  const InfiniteScrollLoader({
    super.key,
    required this.loading,
    required this.paddingBottom,
  });

  final bool loading;
  final double paddingBottom;

  @override
  Widget build(BuildContext context) {
    return SliverVisibility(
      visible: loading,
      sliver: SliverPadding(
        padding: EdgeInsets.only(bottom: paddingBottom + 80, top: 16),
        sliver: SliverToBoxAdapter(
          child: Lottie.asset(
            'assets/lottie/lottie-spinner.json',
            width: 30,
            height: 30,
          ),
        ),
      ),
    );
  }
}
