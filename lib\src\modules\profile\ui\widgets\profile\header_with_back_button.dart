import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../categories/ui/widgets/detail/button_icon_with_background.dart';
import '../../../../offers/controllers/notification_controller.dart';

class HeaderWithBackButton extends StatelessWidget {
  final String title;
  final bool isNotification;
  final bool isOffers;

  const HeaderWithBackButton({
    super.key,
    required this.title,
    this.isNotification = false,
    this.isOffers = false,
  });

  @override
  Widget build(BuildContext context) {
    NotificationController controller = Modular.get<NotificationController>();

    void handleBack(bool isOffers) {
      Modular.to.navigate(
        '/home',
        arguments: {
          'isTransitionPrimary': true,
          'initialIndex': isOffers ? 0 : 2,
        },
      );
    }

    return Container(
      padding: EdgeInsets.only(
        top: Platform.isAndroid ? MediaQuery.of(context).padding.top + 18 : MediaQuery.of(context).padding.top,
        left: 16,
        right: isNotification ? 0 : 16,
        bottom: 16,
      ),
      decoration: BoxDecoration(color: ColorOutlet.surface),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 40,
            height: 40,
            child: ButtonIconWithBackground(
              iconPath: SvgIcons.arrowClearLeft,
              onPressed: () => handleBack(isOffers),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: TextPattern.customText(
              text: title,
              fontSize: 20,
              fontWeightOption: FontWeightOption.bold,
            ),
          ),
          Visibility(
            visible: isNotification,
            child: TextButton(
              onPressed: () => controller.setAllReadNotification(),
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 16),
              ),
              child: TextPattern.customText(
                text: 'Ler todas',
                fontSize: 14,
                color: ColorOutlet.contentPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
