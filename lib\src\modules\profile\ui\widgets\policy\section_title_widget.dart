import 'package:flutter/material.dart';

import '../../../../../components/text_pattern.dart';

class SectionTitle extends StatelessWidget {
  final String title;
  final double fontSize;

  const SectionTitle({required this.title, this.fontSize = 16, super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 24),
      child: TextPattern.customText(
        text: title,

        fontSize: fontSize,
        fontWeightOption: FontWeightOption.bold,
        isSelectable: true,
      ),
    );
  }
}
