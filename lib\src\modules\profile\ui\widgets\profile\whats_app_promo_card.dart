import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/theme/color_outlet.dart';
import 'package:promobell/theme/svg_icons.dart';
import 'package:url_launcher/url_launcher.dart';

class WhatsAppPromoCard extends StatelessWidget {
  const WhatsAppPromoCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 16, right: 16),
      child: InkWell(
        onTap: openPromoBellWhatsAppChannel,
        child: Container(
          clipBehavior: Clip.hardEdge,
          height: 88,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Color(0xFFF0E0D1),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Stack(
            children: [
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                physics: NeverScrollableScrollPhysics(),
                child: Row(
                  children: [
                    SvgPicture.asset(SvgIcons.fundoWhats, fit: BoxFit.fill),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  spacing: 12,
                  children: [
                    Container(
                      height: 64,
                      width: 64,
                      decoration: BoxDecoration(
                        color: Color(0xFF25D366),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      padding: EdgeInsets.all(16),
                      child: SvgPicture.asset(
                        SvgIcons.brandsWhatsApp,
                        colorFilter: ColorFilter.mode(
                          Colors.white,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Column(
                      spacing: 4,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextPattern.customText(
                          text: 'Alertas também no WhatsApp',
                          fontWeightOption: FontWeightOption.semiBold,
                          fontSize: 16,
                        ),
                        Expanded(
                          child: TextPattern.customText(
                            text:
                                'Entre no nosso canal oficial e não perca\nnenhuma oferta. 🥰',
                            color: ColorOutlet.contentGhost,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            lineHeight: 1.3,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Future<void> openPromoBellWhatsAppChannel() async {
  final url = Uri.parse(
    'https://whatsapp.com/channel/0029Vb640YsLY6dEdm01ho2W',
  );

  if (await canLaunchUrl(url)) {
    await launchUrl(url, mode: LaunchMode.externalApplication);
  } else {
    throw 'Não foi possível abrir o canal do WhatsApp.';
  }
}
