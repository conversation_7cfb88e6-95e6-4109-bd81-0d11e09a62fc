import 'package:flutter/material.dart';
import '../../theme/color_outlet.dart';

class DragIndicator extends StatelessWidget {
  const DragIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 44,
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 24),
            child: Container(
              height: 4,
              width: 48,
              decoration: BoxDecoration(
                color: ColorOutlet.systemBorderDisabled,
                borderRadius: BorderRadius.circular(100),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
