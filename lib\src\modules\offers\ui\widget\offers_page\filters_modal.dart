import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell/src/components/drag_indicator.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/src/models/categorias_menu.dart';
import 'package:promobell/src/modules/offers/controllers/offers_controller.dart';
import 'package:promobell/src/modules/offers/ui/widget/notifications_widgets/dotted_line.dart';
import 'package:promobell/src/modules/offers/ui/widget/offers_page/category_image_badg.dart';
import 'package:promobell/src/modules/offers/ui/widget/product_details/styled_logo_container.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/custom_text_input.dart';
import 'package:promobell/src/services/navigation/scroll_services.dart';
import 'package:promobell/theme/color_outlet.dart';
import 'package:promobell/theme/svg_icons.dart';

import '../../../../../helpers/plataform_icons.dart';

class FiltersModal extends StatefulWidget {
  final bool isCategoryFilters;
  final int? intCategoria;
  const FiltersModal({
    super.key,
    required this.isCategoryFilters,
    this.intCategoria,
  });

  @override
  State<FiltersModal> createState() => _FiltersModalState();
}

class _FiltersModalState extends State<FiltersModal> {
  final OffersController controllerOffertas = Modular.get<OffersController>();

  @override
  void initState() {
    super.initState();
    controllerOffertas.initPrecoControllers();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controllerOffertas,
      builder: (context, child) {
        return DraggableScrollableSheet(
          initialChildSize: 0.95,
          maxChildSize: 0.95,
          minChildSize: 0.4,
          builder: (_, controller) {
            return GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              behavior: HitTestBehavior.opaque,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                ),
                child: Column(
                  children: [
                    const DragIndicator(),
                    Expanded(
                      child: ListView(
                        controller: controller,

                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 24),
                            child: TextPattern.customText(
                              text: "Ajuste os filtros como quiser",
                              fontSize: 20,
                              fontWeightOption: FontWeightOption.bold,
                              color: ColorOutlet.contentSecondary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 24),
                            child: TextPattern.customText(
                              text:
                                  "Veja as melhores ofertas dos últimos 90 dias. Filtre por preço, entrega, loja e mais.",
                              fontSize: 14,
                              color: ColorOutlet.contentGhost,
                            ),
                          ),
                          const SizedBox(height: 24),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 24),
                            child: _buildOrdenarSection(controllerOffertas),
                          ),

                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 24),
                            child: _buildPrecoSection(
                              controllerOffertas,
                              isCategoryFilters: widget.isCategoryFilters,
                              intCategoria: widget.intCategoria,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 24),
                            child: _buildLojasSection(
                              controllerOffertas,
                              isCategoryFilters: widget.isCategoryFilters,
                              intCategoria: widget.intCategoria,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 24, right: 0),
                            child: _buildCategoriasSection(
                              intCategoria: widget.intCategoria,
                              isCategoryFilters: widget.isCategoryFilters,
                              controllerOffertas,
                            ),
                          ),
                          const SizedBox(height: 24),
                        ],
                      ),
                    ),

                    AnimatedPadding(
                      duration: const Duration(milliseconds: 000),
                      curve: Curves.easeOut,
                      padding: EdgeInsets.only(
                        top: 16,
                        bottom: MediaQuery.of(context).viewInsets.bottom + 8,
                        left: 8,
                        right: 8,
                      ),
                      child: SafeArea(
                        top: false,
                        child: _buildBottomActions(
                          context,
                          controllerOffertas,
                          isCategoryFilters:
                              widget.isCategoryFilters == true &&
                              widget.intCategoria != null,
                          selectedCategoryId: widget.intCategoria,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}

Widget _buildOrdenarSection(OffersController controller) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      TextPattern.customText(
        text: "Ordenar",
        fontSize: 20,
        fontWeightOption: FontWeightOption.bold,
      ),
      const SizedBox(height: 32),
      Row(
        children: [
          _ordenarButton("Menor preço", "menor_preco", controller),
          const SizedBox(width: 8),
          _ordenarButton("Maior preço", "maior_preco", controller),
        ],
      ),
      const SizedBox(height: 16),
      DottedLine(),
      const SizedBox(height: 30),
    ],
  );
}

Widget _ordenarButton(String label, String value, OffersController controller) {
  final isSelected = controller.ordenarPor == value;
  return Expanded(
    child: OutlinedButton(
      style: OutlinedButton.styleFrom(
        backgroundColor: isSelected
            ? ColorOutlet.contentPrimary
            : ColorOutlet.contentTertiary,
        side: BorderSide(
          color: isSelected
              ? ColorOutlet.contentPrimary
              : ColorOutlet.contentGhost,
        ),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      ),
      onPressed: () => controller.setOrdenarPor(isSelected ? null : value),
      child: Row(
        children: [
          SvgPicture.asset(
            label == "Menor preço"
                ? SvgIcons.arrowTrending
                : SvgIcons.arrowTrendingUp,
            colorFilter: ColorFilter.mode(
              isSelected
                  ? ColorOutlet.contentTertiary
                  : ColorOutlet.contentSecondary,
              BlendMode.srcIn,
            ),
            width: 24,
            height: 24,
          ),
          const SizedBox(width: 8),
          TextPattern.customText(
            text: label,
            color: isSelected
                ? ColorOutlet.contentTertiary
                : ColorOutlet.contentSecondary,
            fontSize: 14,
          ),
        ],
      ),
    ),
  );
}

Widget _buildPrecoSection(
  OffersController controller, {
  required bool isCategoryFilters,
  int? intCategoria,
}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      TextPattern.customText(
        text: "Preço",
        fontSize: 20,
        fontWeightOption: FontWeightOption.bold,
      ),
      const SizedBox(height: 32),
      Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: TextPattern.customText(
                    text: "Mínimo",
                    fontSize: 14,
                    color: ColorOutlet.contentSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                CustomTextInput(
                  isCenter: false,

                  controller: controller.precoMinController,
                  text: "R\$ 0,00",
                  keyboardType: TextInputType.number,
                  validator: controller.precoValidator,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    RealInputFormatter(),
                  ],
                  focusNode: null,
                  textInputAction: TextInputAction.done,
                  onFieldSubmitted: null,
                  onChanged: (value) {
                    final cleanValue = value.replaceAll(RegExp(r'[^0-9]'), '');
                    final parsed = double.tryParse(cleanValue) != null
                        ? double.parse(cleanValue) / 100
                        : null;
                    controller.setPrecoMinimo(
                      parsed,
                      isCategoryFilters: isCategoryFilters,
                      intCategoria: intCategoria,
                    );
                  },
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: TextPattern.customText(
                    text: "Máximo",
                    fontSize: 14,
                    color: ColorOutlet.contentSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                CustomTextInput(
                  isCenter: false,

                  controller: controller.precoMaxController,
                  text: "R\$ 0,00",
                  keyboardType: TextInputType.number,
                  validator: controller.precoValidator,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    RealInputFormatter(),
                  ],
                  focusNode: null,
                  textInputAction: TextInputAction.done,
                  onFieldSubmitted: null,
                  onChanged: (value) {
                    final cleanValue = value.replaceAll(RegExp(r'[^0-9]'), '');
                    final parsed = double.tryParse(cleanValue) != null
                        ? double.parse(cleanValue) / 100
                        : null;
                    controller.setPrecoMaximo(
                      parsed,
                      isCategoryFilters: isCategoryFilters,
                      intCategoria: intCategoria,
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
      const SizedBox(height: 16),
      DottedLine(),
      const SizedBox(height: 30),
    ],
  );
}

final List<String> lojasDisponiveis = [
  "Amazon",
  "Mercado Livre",
  "Magazine Luiza",
  'Shopee',
];
Widget _buildLojasSection(
  OffersController controller, {
  required bool isCategoryFilters,
  int? intCategoria,
}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      TextPattern.customText(
        text: "Lojas",
        fontSize: 20,
        fontWeightOption: FontWeightOption.bold,
      ),
      const SizedBox(height: 32),
      Column(
        children: lojasDisponiveis.map((loja) {
          final isChecked = controller.lojasSelecionadas.contains(loja);

          return GestureDetector(
            onTap: () => controller.toggleLoja(
              loja,
              isCategoryFilters: isCategoryFilters,
              intCategoria: intCategoria,
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  StyledLogoContainer(
                    needpadding: true,
                    padding: 4,
                    logo: PlatformIcons.fromName(loja),
                    plataformName: loja,
                    height: 48,
                    width: 48,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextPattern.customText(
                      text: loja,
                      fontSize: 14,
                      color: ColorOutlet.contentSecondary,
                    ),
                  ),
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: isChecked
                          ? ColorOutlet.contentPrimary
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: isChecked
                            ? ColorOutlet.contentPrimary
                            : ColorOutlet.contentGhost,
                        width: 1.5,
                      ),
                    ),
                    child: isChecked
                        ? Icon(
                            Icons.check,
                            size: 16,
                            color: ColorOutlet.contentTertiary,
                          )
                        : null,
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
      const SizedBox(height: 16),
      DottedLine(),
      const SizedBox(height: 30),
    ],
  );
}

Widget _buildCategoriasSection(
  OffersController controller, {
  bool isCategoryFilters = false,
  int? intCategoria,
}) {
  final categoriasParaExibir = isCategoryFilters && intCategoria != null
      ? controller.categoriasAndSubcategorias
            .where((cat) => cat.id == intCategoria)
            .toList()
      : controller.categoriasAndSubcategorias;

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextPattern.customText(
            text: isCategoryFilters ? "Subcategorias" : "Categorias",
            fontSize: 20,
            fontWeightOption: FontWeightOption.bold,
          ),
          // botao de marcar todas as as subcategorias, so vai aparecer se tiver subcategorias
          if (isCategoryFilters && intCategoria != null)
            Padding(
              padding: const EdgeInsets.only(right: 24),
              child: GestureDetector(
                onTap: () => controller.toggleAllSubcategoriasAPENASPARAFILTOS(
                  intCategoria,
                ),
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color:
                        controller.todasSubcategoriasSelecionadas(intCategoria)
                        ? ColorOutlet.contentPrimary
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color:
                          controller.todasSubcategoriasSelecionadas(
                            intCategoria,
                          )
                          ? ColorOutlet.contentPrimary
                          : ColorOutlet.contentGhost,
                      width: 1.5,
                    ),
                  ),
                  child: controller.todasSubcategoriasSelecionadas(intCategoria)
                      ? const Icon(
                          Icons.check,
                          size: 16,
                          color: ColorOutlet.contentTertiary,
                        )
                      : null,
                ),
              ),
            ),
        ],
      ),
      const SizedBox(height: 32),
      ...categoriasParaExibir.map((categoria) {
        final subcategoriasVisiveis = categoria.subcategorias;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!isCategoryFilters)
              Padding(
                padding: const EdgeInsets.only(top: 16, bottom: 16, right: 12),
                child: Row(
                  children: [
                    InkWell(
                      onTap: () =>
                          controller.toggleCategoriaExpandida(categoria.id),
                      child: Row(
                        children: [
                          CategoryImageBadg(
                            categoria: categoria.nome.toCategoriaMenu(),
                          ),
                          const SizedBox(width: 16),
                          TextPattern.customText(
                            text: categoria.nome,
                            fontSize: 16,
                            fontWeightOption: FontWeightOption.semiBold,
                            color: ColorOutlet.contentSecondary,
                          ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      padding: EdgeInsets.zero,
                      icon: SvgPicture.asset(
                        controller.isCategoriaExpandida(categoria.id)
                            ? SvgIcons.arrowTipUp
                            : SvgIcons.arrowTipDown,
                        colorFilter: const ColorFilter.mode(
                          ColorOutlet.contentSecondary,
                          BlendMode.srcIn,
                        ),
                        width: 24,
                        height: 24,
                      ),
                      onPressed: () =>
                          controller.toggleCategoriaExpandida(categoria.id),
                    ),
                  ],
                ),
              ),

            if (isCategoryFilters ||
                controller.isCategoriaExpandida(categoria.id)) ...[
              Padding(
                padding: const EdgeInsets.only(left: 12, right: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (!isCategoryFilters)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextPattern.customText(
                              text: "Subcategorias",
                              fontSize: 14,
                              fontWeightOption: FontWeightOption.semiBold,
                            ),
                            GestureDetector(
                              onTap: () => controller
                                  .toggleTodasSubcategoriasDaCategoria(
                                    categoria.id,
                                  ),

                              child: Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  color:
                                      controller.todasSubcategoriasSelecionadas(
                                        categoria.id,
                                      )
                                      ? ColorOutlet.contentPrimary
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(6),
                                  border: Border.all(
                                    color:
                                        controller
                                            .todasSubcategoriasSelecionadas(
                                              categoria.id,
                                            )
                                        ? ColorOutlet.contentPrimary
                                        : ColorOutlet.contentGhost,
                                    width: 1.5,
                                  ),
                                ),
                                child:
                                    controller.todasSubcategoriasSelecionadas(
                                      categoria.id,
                                    )
                                    ? const Icon(
                                        Icons.check,
                                        size: 16,
                                        color: ColorOutlet.contentTertiary,
                                      )
                                    : null,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ...subcategoriasVisiveis.map((sub) {
                      final isChecked = controller.subcategoriasSelecionadas
                          .contains(
                            '${categoria.id}:${sub.idSubcategoria}',
                          );

                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: InkWell(
                          onTap: () => controller.toggleSubcategoria(
                            categoria.id,
                            sub.idSubcategoria,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: TextPattern.customText(
                                        text: sub.nome,
                                        fontSize: 14,
                                        color: ColorOutlet.contentSecondary,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  color: isChecked
                                      ? ColorOutlet.contentPrimary
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(6),
                                  border: Border.all(
                                    color: isChecked
                                        ? ColorOutlet.contentPrimary
                                        : ColorOutlet.contentGhost,
                                    width: 1.5,
                                  ),
                                ),
                                child: isChecked
                                    ? const Icon(
                                        Icons.check,
                                        size: 16,
                                        color: ColorOutlet.contentTertiary,
                                      )
                                    : null,
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              ),
            ],
          ],
        );
      }),
    ],
  );
}

Widget _buildBottomActions(
  BuildContext context,
  OffersController controller, {
  required bool isCategoryFilters,
  int? selectedCategoryId,
}) {
  final scrollService = Modular.get<ScrollService>();
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceAround,
    children: [
      TextButton(
        onPressed: () async {
          await controller.limparTodosOsFiltros(
            isCategoryFilters: isCategoryFilters,
          );
          Modular.to.pop();
        },
        child: Center(
          child: TextPattern.customText(
            text: "Limpar filtros",
            fontSize: 14,
            fontWeightOption: FontWeightOption.medium,
            color: ColorOutlet.contentSecondary,
          ),
        ),
      ),

      const SizedBox(width: 12),
      SizedBox(
        height: 44,
        child: ElevatedButton(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
              if (states.contains(WidgetState.disabled)) {
                return ColorOutlet.contentGhost;
              }
              return ColorOutlet.contentPrimary;
            }),
            foregroundColor: WidgetStateProperty.resolveWith<Color>((states) {
              return ColorOutlet.contentTertiary;
            }),
            overlayColor: WidgetStateProperty.all(Colors.transparent),
            shadowColor: WidgetStateProperty.all(Colors.transparent),
            padding: WidgetStateProperty.all(
              const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            ),
            elevation: WidgetStateProperty.all(0),
          ),
          onPressed:
              controller.quantidadeResultadosFiltrados == 0 ||
                  controller.filtrosAtivos == 0
              ? null
              : () async {
                  await controller.buscarProdutosComFiltrosPersonalizados(
                    reset: true,
                    isCategoryFilters: isCategoryFilters,
                    intCategoria: selectedCategoryId,
                  );
                  scrollService.scrollToTop('offersPage');
                  Modular.to.pop();
                },
          child: SizedBox(
            width: 200,
            height: 24,
            child: Stack(
              alignment: Alignment.center,
              children: [
                if (controller.loadingContagemResultados)
                  const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: ColorOutlet.contentTertiary,
                    ),
                  )
                else
                  TextPattern.customText(
                    text: controller.quantidadeResultadosFiltrados != null
                        ? "Ver ${controller.quantidadeResultadosFiltrados} resultados"
                        : "Ver resultados",
                    fontSize: 14,
                    color: ColorOutlet.contentTertiary,
                  ),
              ],
            ),
          ),
        ),
      ),
    ],
  );
}

class RealInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    String digitsOnly = newValue.text.replaceAll(RegExp(r'[^0-9]'), '');

    if (digitsOnly.isEmpty) {
      return TextEditingValue(
        text: '',
        selection: TextSelection.collapsed(offset: 0),
      );
    }

    double value = double.parse(digitsOnly) / 100;

    final formatter = _formatCurrency(value);

    return TextEditingValue(
      text: 'R\$ $formatter',
      selection: TextSelection.collapsed(offset: 'R\$ $formatter'.length),
    );
  }

  String _formatCurrency(double value) {
    String stringValue = value.toStringAsFixed(2);
    List<String> parts = stringValue.split('.');
    String reais = parts[0];
    String centavos = parts[1];

    return '$reais,$centavos'; // Agora com vírgula
  }
}

extension CategoriaStringExtension on String {
  CategoriaMenu toCategoriaMenu() {
    return CategoriaMenu.categorias.firstWhere(
      (cat) => cat.nome == this,
      orElse: () => CategoriaMenu.categorias.first, // fallback seguro
    );
  }
}
