import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/text_pattern.dart';

class CustomDialog {
  static Future<void> show(
    BuildContext context, {
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
    String title = 'Deixar de seguir',
    String message =
        'Ao deixar de seguir, você não receberá mais notificações sobre ofertas exclusivas desta categoria.\n\nVocê poderá seguir novamente a qualquer momento.',
    bool buttonOnly = false,
    String? textOnConfirm,
    String? textOnCancel,
    FontWeightOption fontWeight = FontWeightOption.semiBold,
  }) {
    return showDialog(
      context: context,
      builder: (context) => customDialog(
        context,
        onCancel,
        onConfirm,
        title,
        message,
        buttonOnly,
        textOnConfirm,
        textOnCancel,
        fontWeight,
      ),
    );
  }

  static Widget customDialog(
    BuildContext context,
    VoidCallback? onCancel,
    VoidCallback onConfirm,
    String title,
    String message,
    bool buttonOnly,
    String? textOnConfirm,
    String? textOnCancel,
    FontWeightOption fontWeight,
  ) {
    return Center(
      child: Padding(
        padding: EdgeInsets.only(right: 24, left: 24),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
          ),
          child: Padding(
            padding: EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextPattern.customText(
                  text: title,
                  fontSize: 20,
                  fontWeightOption: fontWeight,
                  decoration: TextDecoration.none,
                ),
                SizedBox(height: 16),
                TextPattern.customText(
                  text: message,
                  fontSize: 14,
                  decoration: TextDecoration.none,
                ),
                SizedBox(height: 32),
                if (!buttonOnly)
                  DialogActions(
                    onConfirm: onConfirm,
                    onCancel: onCancel ?? () {},
                    textOnConfirm: textOnConfirm,
                    textOnCancel: textOnCancel,
                  ),
                if (buttonOnly)
                  Row(
                    children: [
                      Expanded(
                        child: SizedBox(
                          height: 44,
                          child: ElevatedButton(
                            onPressed: () => Modular.to.pop(),
                            child: TextPattern.customText(
                              text: 'Entendi',
                              color: ColorOutlet.paper,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class DialogActions extends StatelessWidget {
  final VoidCallback onConfirm;
  final VoidCallback onCancel;
  final String? textOnConfirm;
  final String? textOnCancel;

  const DialogActions({
    super.key,
    required this.onConfirm,
    required this.onCancel,
    this.textOnConfirm,
    this.textOnCancel,
  });

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Row(
      spacing: 16,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        SizedBox(
          height: 44,
          width: width * .25,
          child: TextButton(
            onPressed: () {
              Modular.to.pop();
              onCancel();
            },
            child: TextPattern.customText(
              text: textOnCancel ?? 'Cancelar',
              fontSize: 14,
            ),
          ),
        ),
        Expanded(
          child: SizedBox(
            height: 44,
            child: ElevatedButton(
              onPressed: () {
                Modular.to.pop();
                onConfirm();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorOutlet.contentPrimary,
                padding: EdgeInsets.symmetric(horizontal: 16),
                overlayColor: ColorOutlet.systemBorderDisabled.withValues(
                  alpha: 0.3,
                ),
              ),
              child: TextPattern.customText(
                text: textOnConfirm ?? 'Deixar de seguir',
                fontSize: 14,
                color: ColorOutlet.paper,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
