import 'package:flutter_modular/flutter_modular.dart';

import '../components/custom_transitions.dart';
import '../core/base/home_base.dart';
import '../core/bindings/controllers_module.dart';
import '../core/bindings/messaging_module.dart';
import '../core/guards/auth_guard.dart';
import '../core/splash_page.dart';
import '../modules/categories/categories_module.dart';
import '../modules/coupons/coupons_module.dart';
import '../modules/offers/offers_module.dart';
import '../modules/profile/profile_module.dart';
import '../services/deep_link_route_handler.dart';

class AppModule extends Module {
  static const String splashRoute = '/';
  static const String homeRoute = '/home';
  static const String offersModule = '/offers';
  static const String couponsModule = '/coupons';
  static const String categoriesModule = '/categories';
  static const String profileModule = '/profile';

  // Rotas para deep links
  static const String productRoute = '/product';
  static const String categoryRoute = '/category';

  @override
  List<Module> get imports => [
    ControllersModule(),
    MessagingModule(),
  ];

  @override
  void routes(RouteManager r) {
    r.child(splashRoute, child: (_) => const SplashPage());
    r.child(
      homeRoute,
      child: (_) => const HomeBasePage(),
      guards: [AuthGuard()],
      transition: TransitionType.custom,
      customTransition: CustomTransition(
        transitionBuilder:
            (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              final data = r.args.data;
              final isTransitionPrimary =
                  (data is Map ? data['isTransitionPrimary'] : false) ?? false;

              if (isTransitionPrimary) {
                return CustomTransitions.fromLeft().transitionBuilder(
                  context,
                  animation,
                  secondaryAnimation,
                  child,
                );
              } else {
                return CustomTransitions.fadeIn().transitionBuilder(
                  context,
                  animation,
                  secondaryAnimation,
                  child,
                );
              }
            },
      ),
      duration: const Duration(milliseconds: 200),
    );
    r.module(
      offersModule,
      module: OffersModule(),
      guards: [AuthGuard()],
      transition: TransitionType.scale,
      duration: const Duration(milliseconds: 200),
    );
    r.module(
      couponsModule,
      module: CouponsModule(),
      guards: [AuthGuard()],
      transition: TransitionType.scale,
      duration: const Duration(milliseconds: 200),
    );
    r.module(
      categoriesModule,
      module: CategoriesModule(),
      guards: [AuthGuard()],
      transition: TransitionType.scale,
      duration: const Duration(milliseconds: 200),
    );
    r.module(profileModule, module: ProfileModule());

    // Rotas para deep links
    r.child(
      productRoute,
      child: (context) => DeepLinkRouteHandler(
        routeType: 'product',
        params: r.args.data,
      ),
      guards: [AuthGuard()],
      transition: TransitionType.fadeIn,
      duration: const Duration(milliseconds: 100),
    );
    r.child(
      categoryRoute,
      child: (context) => DeepLinkRouteHandler(
        routeType: 'category',
        params: r.args.data,
      ),
      guards: [AuthGuard()],
      transition: TransitionType.fadeIn,
      duration: const Duration(milliseconds: 100),
    );
  }
}
