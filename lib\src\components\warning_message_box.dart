import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell/src/components/text_pattern.dart';

class WarningMessageBox extends StatelessWidget {
  final String? message;
  final String icon;
  final String title;
  final Color color;
  final bool noMessage;
  final bool iconOnTheRight;
  final double paddingHorizontal;
  final double paddingVertical;
  final double radius;
  final double fontSize;
  final double iconSize;
  final void Function()? onPressed;

  const WarningMessageBox({
    super.key,
    this.message,
    required this.icon,
    required this.title,
    required this.color,
    this.noMessage = true,
    this.iconOnTheRight = false,
    this.paddingHorizontal = 16,
    this.paddingVertical = 16,
    this.radius = 16,
    this.fontSize = 14,
    this.iconSize = 24,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: iconOnTheRight
              ? const EdgeInsets.only(right: 0, left: 16, top: 0, bottom: 16)
              : EdgeInsets.symmetric(
                  horizontal: paddingHorizontal,
                  vertical: paddingVertical,
                ),
          decoration: BoxDecoration(
            color: Color.alphaBlend(
              const Color.fromRGBO(255, 255, 255, 0.56),
              color,
            ),
            borderRadius: BorderRadius.circular(radius),
            border: iconOnTheRight ? null : Border.all(color: color, width: 1),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: iconOnTheRight
                    ? [
                        Expanded(
                          child: TextPattern.customText(
                            text: title,
                            fontSize: 14,
                            fontWeightOption: FontWeightOption.semiBold,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        IconButton(
                          onPressed: onPressed,
                          icon: SvgPicture.asset(icon, width: 24, height: 24),
                        ),
                      ]
                    : [
                        SvgPicture.asset(
                          icon,
                          width: iconSize,
                          height: iconSize,
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: TextPattern.customText(
                            text: title,
                            fontSize: fontSize,
                            fontWeightOption: FontWeightOption.semiBold,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
              ),
              if (noMessage && message != null) ...[
                Padding(
                  padding: EdgeInsets.only(
                    right: iconOnTheRight ? 16 : 0,
                    top: iconOnTheRight ? 0 : 8,
                    left: iconOnTheRight ? 0 : 32,
                  ),
                  child: TextPattern.customText(text: message!, fontSize: 14),
                ),
              ],
            ],
          ),
        ),
        if (iconOnTheRight)
          Row(
            children: [
              SizedBox(width: 214),
              CustomPaint(
                size: const Size(16, 8),
                painter: TrianglePainter(
                  color: Color.alphaBlend(
                    const Color.fromRGBO(255, 255, 255, 0.56),
                    color,
                  ),
                ),
              ),
            ],
          ),
      ],
    );
  }
}

class TrianglePainter extends CustomPainter {
  final Color color;

  const TrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    Path path = Path();
    path.moveTo(0, 0);
    path.lineTo(size.width, 0);
    path.lineTo(size.width / 2, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
