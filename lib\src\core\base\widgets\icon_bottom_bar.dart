import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../theme/color_outlet.dart';
import '../../../../theme/svg_icons.dart';
import '../../../components/shimmer_widget.dart';
import '../../../services/logs/app_logger.dart';
import '../controllers/base_controller/base_controller.dart';

class IconBottomBar extends StatefulWidget {
  final String image;
  final Uint8List? tempImage;
  final String svg;
  final String label;
  final int currentIndex;
  final void Function()? onTap;

  const IconBottomBar({
    super.key,
    this.image = '',
    this.tempImage,
    this.svg = SvgIcons.markerVerified,
    required this.currentIndex,
    required this.onTap,
    required this.label,
  });

  @override
  State<IconBottomBar> createState() => _IconBottomBarState();
}

class _IconBottomBarState extends State<IconBottomBar> {
  final controller = Modular.get<BaseController>();
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) => InkWell(
        splashColor: ColorOutlet.systemBorderDisabled.withValues(
          alpha: 0.3,
        ),
        highlightColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
        onTap: () {
          widget.onTap?.call();
          controller.index = widget.currentIndex;
        },
        borderRadius: BorderRadius.circular(24),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              widget.image != ''
                  ? Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: widget.tempImage != null
                            ? Image.memory(
                                widget.tempImage!,
                                fit: BoxFit.cover,
                              )
                            : widget.image.isEmpty
                            ? SvgPicture.asset(
                                widget.svg,
                                colorFilter: ColorFilter.mode(
                                  widget.currentIndex == controller.index
                                      ? ColorOutlet.contentPrimary
                                      : ColorOutlet.contentSecondary,
                                  BlendMode.srcIn,
                                ),
                                height: 26,
                              )
                            : Image.network(
                                widget.image,
                                fit: BoxFit.cover,
                                loadingBuilder:
                                    (
                                      context,
                                      child,
                                      loadingProgress,
                                    ) {
                                      if (loadingProgress == null) {
                                        return child;
                                      } else {
                                        return ShimmerWidget(
                                          height: 24,
                                          width: 24,
                                          radius: 8,
                                        );
                                      }
                                    },
                                errorBuilder:
                                    (
                                      context,
                                      error,
                                      stackTrace,
                                    ) {
                                      AppLogger.logError(
                                        'Erro ao carregar imagem do perfil',
                                        error,
                                        stackTrace,
                                      );
                                      return SvgPicture.asset(
                                        widget.svg,
                                        colorFilter: ColorFilter.mode(
                                          widget.currentIndex ==
                                                  controller.index
                                              ? ColorOutlet.contentPrimary
                                              : ColorOutlet.contentSecondary,
                                          BlendMode.srcIn,
                                        ),
                                        height: 26,
                                      );
                                    },
                              ),
                      ),
                    )
                  : widget.svg != '' || widget.svg != SvgIcons.markerVerified
                  ? widget.currentIndex == 0
                        ? SvgPicture.asset(widget.svg, height: 26)
                        : SvgPicture.asset(
                            widget.svg,
                            colorFilter: ColorFilter.mode(
                              widget.currentIndex == controller.index
                                  ? ColorOutlet.contentPrimary
                                  : ColorOutlet.contentSecondary,
                              BlendMode.srcIn,
                            ),
                            height: 26,
                          )
                  : CircularProgressIndicator(),
              SizedBox(height: 5),
              Text(
                widget.label,
                style: TextStyle(
                  color: widget.currentIndex == controller.index
                      ? ColorOutlet.contentPrimary
                      : ColorOutlet.contentSecondary,
                  fontSize: 10,
                  fontWeight: widget.currentIndex == controller.index
                      ? FontWeight.w600
                      : FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
