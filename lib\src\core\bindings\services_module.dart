import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/core/bindings/core_module.dart';

import '../../services/navigation/scroll_services.dart';
import '../../services/supabase/connection/i_connection.dart';
import '../../services/supabase/db/db.dart';
import '../../services/supabase/produtos/get/get_produtos.dart';
import '../../services/supabase/usuarios/get/get_usuarios.dart';
import '../../services/supabase/usuarios/post/post_usuarios.dart';
import '../../services/supabase/usuarios/put/put_usuarios.dart';

class ServicesModule extends Module {
  @override
  List<Module> get imports => [CoreModule()];

  @override
  void exportedBinds(i) {
    i.addSingleton(
      () => GetUsuarios(connection: i.get<IConnection>(), db: i.get<DB>()),
    );
    i.addSingleton(
      () => PostUsuarios(connection: i.get<IConnection>(), db: i.get<DB>()),
    );
    i.addSingleton(
      () => PutUsuarios(connection: i.get<IConnection>(), db: i.get<DB>()),
    );
    i.addSingleton(
      () => GetProdutos(connection: i.get<IConnection>(), db: i.get<DB>()),
    );
    i.addSingleton(ScrollService.new);
  }
}
