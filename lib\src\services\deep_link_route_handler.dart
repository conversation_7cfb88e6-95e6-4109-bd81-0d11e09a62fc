import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:lottie/lottie.dart';
import 'package:promobell/theme/color_outlet.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../app_links_service.dart';
import '../app/app_module.dart';
import '../core/base/controllers/base_controller/base_controller.dart';
import '../models/categorias_menu.dart';
import '../models/product.dart';
import '../modules/categories/categories_module.dart';
import '../modules/offers/controllers/offers_controller.dart';
import '../modules/offers/offers_module.dart';
import 'logs/app_logger.dart';

class DeepLinkRouteHandler extends StatefulWidget {
  final Map<String, dynamic>? params;
  final String routeType;

  const DeepLinkRouteHandler({
    super.key,
    required this.routeType,
    this.params,
  });

  @override
  State<DeepLinkRouteHandler> createState() => _DeepLinkRouteHandlerState();
}

class _DeepLinkRouteHandlerState extends State<DeepLinkRouteHandler> {
  final baseController = Modular.get<BaseController>();
  bool _isProcessing = true;
  bool _hasError = false;
  Timer? _timeoutTimer;

  @override
  void initState() {
    super.initState();
    _startTimeout();
    _processDeepLink();
  }

  @override
  void dispose() {
    _timeoutTimer?.cancel();
    super.dispose();
  }

  void _startTimeout() {
    _timeoutTimer = Timer(const Duration(seconds: 10), () {
      if (mounted && _isProcessing) {
        setState(() {
          _isProcessing = false;
          _hasError = true;
        });
      }
    });
  }

  Future<void> _processDeepLink() async {
    try {
      if (widget.routeType == 'product') {
        await _handleProductRoute();
      } else if (widget.routeType == 'category') {
        await _handleCategoryRoute();
      } else {
        _navigateToHome();
      }
    } catch (e, stackTrace) {
      AppLogger.logError(
        'Erro ao processar deep link: ${widget.routeType}',
        e,
        stackTrace,
      );
      if (mounted) {
        setState(() {
          _isProcessing = false;
          _hasError = true;
        });
      }
    }
  }

  Future<void> _handleProductRoute() async {
    try {
      final String? id = widget.params?['id'];

      if (id == null || id.isEmpty || id == "0") {
        _navigateToHome();
        return;
      }

      final product = await _getProduct(id);

      if (mounted) {
        _timeoutTimer?.cancel();
        setState(() {
          _isProcessing = false;
        });

        Modular.to
            .pushNamedAndRemoveUntil(
              AppModule.homeRoute,
              (route) => false,
              arguments: {'initialIndex': 0, 'maintainIndex': false},
            )
            .then((_) {
              Modular.get<OffersController>().refreshProdutos();
            });

        Modular.to.pushNamed(
          '/offers${OffersModule.productDetails}',
          arguments: product,
        );
      }
    } catch (e) {
      if (mounted) {
        _timeoutTimer?.cancel();
        setState(() {
          _isProcessing = false;
          _hasError = true;
        });
      }
    }
  }

  Future<void> _handleCategoryRoute() async {
    try {
      final categoria = widget.params?['categoria'];
      if (categoria != null && categoria is CategoriaMenu) {
        if (mounted) {
          _timeoutTimer?.cancel();
          setState(() {
            _isProcessing = false;
          });

          await Modular.to.pushNamedAndRemoveUntil(
            AppModule.homeRoute,
            (route) => false,
            arguments: {'initialIndex': 1, 'maintainIndex': false},
          );
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Modular.to.pushNamed(
              '/categories${CategoriesModule.detailsCategoryPage}',
              arguments: categoria,
            );
          });
        }
        return;
      }

      final idParam = widget.params?['id'];
      if (idParam != null) {
        final int id = int.tryParse(idParam.toString()) ?? -1;
        final categoriaById = CategoriaMenu.getCategoriaById(id);

        if (categoriaById != null) {
          if (mounted) {
            _timeoutTimer?.cancel();
            setState(() {
              _isProcessing = false;
            });

            Modular.to.pushNamed(
              AppModule.homeRoute,
              arguments: {'initialIndex': 1, 'maintainIndex': false},
            );
            Modular.to.pushNamed(
              '/categories${CategoriesModule.detailsCategoryPage}',
              arguments: categoriaById,
            );
          }
          return;
        } else {
          AppLogger.logInfo(
            '⚠️ DeepLinkRouteHandler: Categoria não encontrada para o ID: $id',
          );
        }
      }

      final String? categoryName = widget.params?['name'];
      if (categoryName != null && categoryName.isNotEmpty) {
        final CategoriaMenu categoriaByName = CategoriaMenu.getCategoriaByNome(
          categoryName,
        );
        if (mounted) {
          _timeoutTimer?.cancel();
          setState(() {
            _isProcessing = false;
          });

          Modular.to.pushNamed(
            AppModule.homeRoute,
            arguments: {'initialIndex': 1, 'maintainIndex': false},
          );
          Modular.to.pushNamed(
            '/categories${CategoriesModule.detailsCategoryPage}',
            arguments: categoriaByName,
          );
        }
        return;
      }

      if (mounted) {
        _timeoutTimer?.cancel();
        setState(() {
          _isProcessing = false;
        });

        // Navegar para home com índice de categoria
        Modular.to.pushReplacementNamed(
          '/home',
          arguments: {'initialIndex': 1, 'maintainIndex': false},
        );
      }
    } catch (e, stackTrace) {
      AppLogger.logError(
        'DeepLinkRouteHandler: Erro ao processar rota de categoria',
        e,
        stackTrace,
      );
      if (mounted) {
        _timeoutTimer?.cancel();
        setState(() {
          _isProcessing = false;
          _hasError = true;
        });
      }
    }
  }

  void _navigateToHome() {
    if (mounted) {
      final int targetIndex = widget.routeType == 'category' ? 1 : 0;

      Modular.to.pushNamedAndRemoveUntil(
        '/home',
        (route) => false,
        arguments: {
          'initialIndex': targetIndex,
          'maintainIndex': false,
        },
      );
    }
  }

  Future<Product> _getProduct(String id) async {
    final int idProduto = int.parse(id);
    final supabase = Supabase.instance.client;

    try {
      final data = await supabase
          .from('produtos_cadastro')
          .select()
          .eq('id', idProduto)
          .select();

      if (data.isEmpty) {
        throw Exception(
          'Produto não encontrado para o ID: $idProduto',
        );
      }

      final Product produto = Product.fromMap(data.first);
      AppLogger.logInfo(
        'DeepLinkRouteHandler: Produto recuperado com sucesso: ${produto.titulo}',
      );
      return produto;
    } catch (e, stackTrace) {
      AppLogger.logError(
        'DeepLinkRouteHandler: Erro ao buscar produto',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  _handleBackButton() {
    _timeoutTimer?.cancel();
    if (mounted) {
      // Reset da flag de deep link
      AppLinksService.hasReceivedLink = false;

      Modular.to.pushNamedAndRemoveUntil(
        '/home',
        (route) => false,
        arguments: {'initialIndex': 0, 'maintainIndex': false},
      );
    }
  }

  void _onPopInvoked(bool didPop, dynamic result) {
    if (didPop) return;
    _handleBackButton();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: _onPopInvoked,
      child: Scaffold(
        backgroundColor: ColorOutlet.contentPrimary,
        body: Center(
          child: _hasError
              ? _handleBackButton()
              : _isProcessing
              ? Lottie.asset(
                  'assets/lottie/lottie-logo.json',
                  width: 120,
                  height: 120,
                  fit: BoxFit.cover,
                  repeat: true,
                  animate: true,
                )
              : const SizedBox.shrink(),
        ),
      ),
    );
  }
}
