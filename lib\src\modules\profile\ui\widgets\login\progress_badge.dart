import 'package:flutter/material.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/theme/color_outlet.dart';

class ProgressBadge extends StatelessWidget {
  final String text;

  const ProgressBadge({required this.text, super.key});

  @override
  Widget build(BuildContext context) {
    final paddingTop = MediaQuery.of(context).padding.top;

    return Row(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 32 + paddingTop, bottom: 24),
          child: Container(
            height: 40,
            width: 40,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: ColorOutlet.contentPrimary,
              borderRadius: BorderRadius.circular(16),
            ),
            child: TextPattern.customText(
              text: text,
              color: ColorOutlet.contentTertiary,
            ),
          ),
        ),
      ],
    );
  }
}
