import 'package:flutter/material.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../offers/ui/widget/offers_page/filter_products_widget.dart';
import '../../../controllers/categories_controller.dart';
import '../../pages/details_category_page.dart';
import 'sliver_app_bar_delegate.dart';

class PersistentFilterHeader extends StatelessWidget {
  final CategoriesController controller;
  final DetailsCategoryPage widget;

  const PersistentFilterHeader({
    super.key,
    required this.controller,
    required this.widget,
  });

  @override
  Widget build(BuildContext context) {
    final Color color = controller.getFilteredColor(widget.category);
    double height = 64;

    return SliverPersistentHeader(
      pinned: true,
      delegate: SliverAppBarDelegate(
        maxHeight: height,
        minHeight: height,
        child: Stack(
          alignment: Alignment.center,
          children: [
            Row(
              children: [
                Container(
                  color: controller.scrollProgress > 0.99
                      ? color
                      : Colors.transparent,
                  height: height,
                  width: controller.scrollProgress > 0.99 ? 17.3 : 16,
                ),
                Flexible(
                  child: Container(
                    color: controller.scrollProgress > 0.08
                        ? ColorOutlet.paper
                        : Colors.transparent,
                  ),
                ),
                Container(
                  color: controller.scrollProgress > 0.99
                      ? color
                      : Colors.transparent,
                  height: height,
                  width: controller.scrollProgress > 0.99 ? 17.3 : 16,
                ),
              ],
            ),
            Container(
              height: height,
              decoration: BoxDecoration(
                color: ColorOutlet.surface,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
              ),
            ),
            FilterProductsWidget(
              isFilterForCategory: true,
              isCategoryFilters: true,
              intCategoria: widget.category.id,
            ),
            if (controller.scrollProgress > 0.99)
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  height: 1,
                  width: double.infinity,
                  color: ColorOutlet.systemBorderDisabled,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
