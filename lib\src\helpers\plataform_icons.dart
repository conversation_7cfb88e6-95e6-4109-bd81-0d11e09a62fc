import '../../theme/svg_icons.dart';

class PlatformIcons {
  static const Map<String, String> _icons = {
    'mercado livre': SvgIcons.iconeMercadoLivre,
    'mercadolivre': SvgIcons.iconeMercadoLivre,
    'magalu': SvgIcons.iconeMagalu,
    'magazine luiza': SvgIcons.iconeMagaluPNG,
    'magazineluiza': SvgIcons.iconeMagaluPNG,
    'amazon': SvgIcons.iconeAmazon,
    'shopee': SvgIcons.iconeShopee,
  };

  static String fromName(String name) {
    final normalized = name.trim().toLowerCase().replaceAll(' ', '');
    final result = _icons[normalized] ?? '';
    return result;
  }
}
