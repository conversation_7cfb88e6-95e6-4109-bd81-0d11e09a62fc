import 'package:flutter/material.dart';
import '../../theme/color_outlet.dart';
import '../../theme/line_height.dart';

class FieldPattern extends StatefulWidget {
  final TextEditingController? controller;
  final String? hintText;
  final String? Function(String?)? validator;
  final Function()? onTapOutside;
  final Function(String)? onChanged;
  final double? height;

  const FieldPattern({
    super.key,
    this.controller,
    this.hintText,
    this.validator,
    this.onTapOutside,
    this.onChanged,
    this.height,
  });

  @override
  State<FieldPattern> createState() => _FieldPatternState();
}

class _FieldPatternState extends State<FieldPattern> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height ?? 52,
      child: TextFormField(
        expands: true,
        maxLines: null,
        minLines: null,
        controller: widget.controller,
        textAlignVertical: TextAlignVertical.top,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          height: LineHeight.lh20f14,
          color: ColorOutlet.contentSecondary,
        ),
        decoration: InputDecoration(
          hintText: widget.hintText,
          contentPadding: EdgeInsets.all(16),
          hintStyle: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            height: LineHeight.lh20f14,
            color: ColorOutlet.contentGhost,
          ),
          filled: true,
          fillColor: ColorOutlet.paper,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(width: 2, color: ColorOutlet.contentGhost),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(width: 1, color: ColorOutlet.contentGhost),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(width: 1, color: ColorOutlet.contentPrimary),
          ),
        ),
        validator: widget.validator,
        onChanged: widget.onChanged,
        onTapOutside: (event) {
          FocusScope.of(context).unfocus();
          widget.onTapOutside?.call();
        },
      ),
    );
  }
}
