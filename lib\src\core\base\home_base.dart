import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../app_links_service.dart';
import '../../components/scaffold_template.dart';
import '../../models/store_notifications_model.dart';
import '../../models/user_profile_model.dart' show UserProfileModel;
import '../../modules/categories/ui/pages/categories_page.dart';
import '../../modules/offers/controllers/notification_controller.dart';
import '../../modules/offers/controllers/offers_controller.dart';
import '../../modules/offers/ui/pages/offers_page.dart';
import '../../modules/profile/controllers/profile_controller.dart';
import '../../modules/profile/ui/pages/profile/profile_page.dart';
import '../../services/navigation/scroll_services.dart';
import 'controllers/base_controller/base_controller.dart';

class HomeBasePage extends StatefulWidget {
  const HomeBasePage({super.key});

  @override
  State<HomeBasePage> createState() => _HomeBasePageState();
}

class _HomeBasePageState extends State<HomeBasePage> {
  final controllerOfertas = Modular.get<OffersController>();
  final controller = Modular.get<BaseController>();
  final profileController = Modular.get<ProfileController>();
  final notificationController = Modular.get<NotificationController>();
  final _scrollService = Modular.get<ScrollService>();
  final baseController = Modular.get<BaseController>();

  @override
  void initState() {
    super.initState();
    _initializeUser();
    init();
    _notificacao();

    controllerOfertas.initSavedProducts();
    controllerOfertas.getCategoriasAndSubcategorias();

    final initialIndex = Modular.args.data?['initialIndex'] ?? 0;
    final maintainIndex = Modular.args.data?['maintainIndex'] ?? false;

    controller.pageController = PageController(
      initialPage: initialIndex,
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.navPage(initialIndex);
      controllerOfertas.refreshProdutos();

      // Se não é para manter o índice, reseta a flag de deep link
      if (!maintainIndex && AppLinksService.hasReceivedLink) {
        // Pequeno delay para garantir que a navegação foi concluída
        Future.delayed(const Duration(milliseconds: 100), () {
          AppLinksService.hasReceivedLink = false;
        });
      }
    });
  }

  Future<void> _initializeUser() async {
    while (Supabase.instance.client.auth.currentSession?.user.email == null) {
      await Future.delayed(Duration(milliseconds: 100));
    }
    await profileController.loadUserData();
    final prefs = await SharedPreferences.getInstance();
    String? email = prefs.getString('userEmail');
    await profileController.loadUserProfile(
      Supabase.instance.client.auth.currentSession?.user.email ?? email ?? '',
    );
    await controller.tokenFCM(
      profileController.userProfile ?? UserProfileModel.empty(),
    );
  }

  Future<void> init() async {
    if (!AppLinksService.hasReceivedLink) {
      await AppLinksService().initAppLinks();
    }

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!AppLinksService.hasReceivedLink) {
        var (precisaAtualizar, ehForcada) = await controllerOfertas
            .verificarAtualizacaoDoApp();

        if (precisaAtualizar) {
          if (ehForcada) {
            Modular.to.navigate('/offers/attPage', arguments: false);
          } else {
            if (controllerOfertas.precisaAtualizar) {
              Modular.to.navigate('/offers/attPage', arguments: true);
            }
          }
        }
      }

      notificationController.checkNotification();
      notificationController.getNotifications();
      notificationController.deleteNotificationOld();
    });
  }

  _notificacao() async {
    await notificationController.observarNotification((value) {
      if (value != StoreNotificationsModel.empty()) {
        notificationController.getNotifications();
      }
      return notificationController.getNotifications();
    });
  }

  @override
  void dispose() {
    _scrollService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return ScaffoldTemplate(
          isPopNavigator: true,
          isPopScope: true,
          body: SafeArea(
            top: false,
            bottom: false,
            child: PageView(
              physics: ClampingScrollPhysics(),
              controller: controller.pageController,
              onPageChanged: (index) {
                controller.navPage(index);
                if (index == 0) {
                  controllerOfertas.refreshProdutos();
                }
              },
              children: const [
                OffersPage(),
                CategoriesPage(),
                ProfilePage(),
              ],
            ),
          ),
        );
      },
    );
  }
}
