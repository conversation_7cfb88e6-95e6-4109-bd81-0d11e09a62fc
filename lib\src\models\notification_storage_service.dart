import 'dart:convert';

class NotificationStorageService {
  final int? id;
  final String? title;
  final String? body;
  final String? date;
  final int? idProduto;
  final String? email;
  int? isRead;

  NotificationStorageService({
    this.id,
    this.title,
    this.body,
    this.date,
    this.isRead,
    this.email,
    this.idProduto,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'date': date,
      'isRead': isRead,
      'email': email,
      'idProduto': idProduto,
    };
  }

  factory NotificationStorageService.fromMap(
    Map<String, dynamic> map,
  ) {
    return NotificationStorageService(
      id: map['id'],
      title: map['title'],
      body: map['body'],
      date: map['date'],
      isRead: map['isRead'],
      email: map['email'],
      idProduto: map['idProduto'],
    );
  }

  String toJson() => json.encode(toMap());

  factory NotificationStorageService.fromJson(String source) =>
      NotificationStorageService.fromMap(json.decode(source));

  factory NotificationStorageService.empty() {
    return NotificationStorageService(
      id: 0,
      title: '',
      body: '',
      date: '',
      isRead: 0,
    );
  }
}
