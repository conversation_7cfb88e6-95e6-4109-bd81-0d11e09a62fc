import 'dart:convert';
import 'package:flutter/foundation.dart';

class StoreNotificationsModel {
  int id;
  String title;
  String message;
  DateTime date;
  String? email;
  int? idproduto;

  StoreNotificationsModel({
    required this.id,
    required this.title,
    required this.message,
    required this.date,
    this.email,
    this.idproduto,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'title': title,
      'message': message,
      'date': date.toIso8601String(),
      'email': email,
      'idproduto': idproduto,
    };
  }

  factory StoreNotificationsModel.fromMap(Map<String, dynamic> map) {
    return StoreNotificationsModel(
      id: map['id'] as int,
      title: map['title'] as String,
      message: map['message'] as String,
      date: _parseDateTime(map['date'].toString()) ?? DateTime.now(),
      email: map['email'] as String?,
      idproduto: map['idproduto'] as int?,
    );
  }

  static DateTime? _parseDateTime(String? dateTimeString) {
    if (dateTimeString == null || dateTimeString.isEmpty) {
      return null;
    }
    try {
      return DateTime.parse(dateTimeString);
    } catch (e) {
      if (kDebugMode) {
        if (kDebugMode) {
          print("Erro ao converter data e hora: $e");
        }
      }
      return null;
    }
  }

  String toJson() => json.encode(toMap());

  factory StoreNotificationsModel.fromJson(String source) =>
      StoreNotificationsModel.fromMap(
        json.decode(source) as Map<String, dynamic>,
      );

  factory StoreNotificationsModel.empty() {
    return StoreNotificationsModel(
      id: 0,
      title: '',
      message: '',
      date: DateTime.now(),
    );
  }
}
