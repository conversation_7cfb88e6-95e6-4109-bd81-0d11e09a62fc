import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

import '../../../../components/text_pattern.dart';

class EmptyStateSearchWidget extends StatelessWidget {
  const EmptyStateSearchWidget({
    super.key,
  });

  @override
  Widget build(
    BuildContext context,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 24),
          Lottie.asset('assets/lottie/pesquisar-fina.json', width: 120, height: 120, fit: BoxFit.cover),
          const SizedBox(height: 24),
          TextPattern.customText(text: 'Não encontramos nada', fontSize: 16, fontWeightOption: FontWeightOption.semiBold),
          const SizedBox(height: 16),
          SizedBox(
            width: 300,
            child: TextPattern.customText(text: 'Tente buscar com outras palavras. A gente vai te ajudar a encontrar o que procura.', fontSize: 14, textAlign: TextAlign.center),
          ),
        ],
      ),
    );
  }
}
