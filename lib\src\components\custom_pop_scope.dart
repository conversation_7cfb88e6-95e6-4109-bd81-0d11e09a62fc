import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../core/base/controllers/base_controller/base_controller.dart';

class CustomPopScope extends StatelessWidget {
  final int index;
  final Widget child;

  const CustomPopScope({
    super.key,
    required this.child,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    final controllerBase = Modular.get<BaseController>();

    void handlePop(bool didPop, dynamic result) {
      Modular.to.navigate(
        '/home',
        arguments: {
          'maintainIndex': true,
          'isTransitionPrimary': true,
        },
      );
      controllerBase.navPage(index);
    }

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: handlePop,
      child: child,
    );
  }
}
