import 'package:flutter/material.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/theme/color_outlet.dart';

class CustomRadioTile extends StatelessWidget {
  final String value;
  final String? groupValue;
  final ValueChanged<String?> onChanged;
  final String text;
  final double fontSize;

  const CustomRadioTile({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    required this.text,
    this.fontSize = 16,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        splashColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
        highlightColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
        focusColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
        onTap: () => onChanged(value),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextPattern.customText(text: text, fontSize: fontSize),
              SizedBox(
                height: 24,
                width: 24,
                child: Transform.scale(
                  scale: 1.4,
                  child: Radio<String>(
                    hoverColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    splashRadius: 0,
                    value: value,
                    groupValue: groupValue,
                    onChanged: onChanged,
                    activeColor: ColorOutlet.contentPrimary,
                    fillColor: WidgetStateProperty.resolveWith<Color>((
                      Set<WidgetState> states,
                    ) {
                      if (states.contains(WidgetState.disabled)) {
                        return ColorOutlet.contentGhost;
                      }
                      if (states.contains(WidgetState.selected)) {
                        return ColorOutlet.contentPrimary;
                      }
                      return ColorOutlet.contentGhost;
                    }),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
