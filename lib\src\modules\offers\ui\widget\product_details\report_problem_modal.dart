import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/custom_snack_bar.dart';
import '../../../../../components/drag_indicator.dart';
import '../../../../../components/text_pattern.dart';
import '../../../controllers/offers_controller.dart';

class ReportProblemModal extends StatefulWidget {
  final int idProduto;
  final bool emCimaOuEmbaixo;
  const ReportProblemModal({
    super.key,
    required this.idProduto,
    required this.emCimaOuEmbaixo,
  });

  @override
  State<ReportProblemModal> createState() => _ReportProblemModalState();
}

class _ReportProblemModalState extends State<ReportProblemModal> {
  OffersController controller = Modular.get<OffersController>();
  String? _selectedProblem;

  final List<Map<String, String>> problems = [
    {"icon": SvgIcons.financeDiscountBroken, "title": "Oferta indisponível"},
    {"icon": SvgIcons.financePriceBroken, "title": "Valor diferente"},
    {"icon": SvgIcons.financeCouponBroken, "title": "Cupom expirado"},
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 24,
        right: 24,
        bottom: MediaQuery.of(context).viewInsets.bottom + 43,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DragIndicator(),
          TextPattern.customText(
            text: "Relatar problema",
            fontSize: 20,
            fontWeightOption: FontWeightOption.bold,
          ),
          SizedBox(height: 16),
          TextPattern.customText(
            text:
                "Se algo não estiver funcionando corretamente, envie seu feedback para que possamos melhorar nossas publicações.",
            fontSize: 14,
          ),
          SizedBox(height: 16),
          Column(
            spacing: 16,
            children: problems.map((problem) {
              return InkWell(
                splashColor: ColorOutlet.systemBorderDisabled.withValues(
                  alpha: 0.3,
                ),
                highlightColor: ColorOutlet.systemBorderDisabled.withValues(
                  alpha: 0.3,
                ),
                onTap: () {
                  setState(() {
                    _selectedProblem = problem["title"]!;
                  });
                },
                borderRadius: BorderRadius.circular(16),
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset(
                            problem["icon"]!,
                            width: 24,
                            height: 24,
                          ),
                          SizedBox(width: 8),
                          TextPattern.customText(
                            text: problem["title"]!,
                            fontSize: 14,
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 24,
                        width: 24,
                        child: Transform.scale(
                          scale: 1.3,
                          child: Radio<String>(
                            hoverColor: Colors.transparent,
                            focusColor: Colors.transparent,
                            splashRadius: 0,
                            value: problem["title"]!,
                            groupValue: _selectedProblem,
                            onChanged: (value) {
                              setState(() {
                                _selectedProblem = value;
                              });
                            },
                            activeColor: ColorOutlet.contentPrimary,
                            // cor desativada
                            fillColor: WidgetStateProperty.resolveWith<Color>((
                              Set<WidgetState> states,
                            ) {
                              if (states.contains(WidgetState.disabled)) {
                                return ColorOutlet.contentGhost;
                              }
                              if (states.contains(WidgetState.selected)) {
                                return ColorOutlet.contentPrimary;
                              }
                              return ColorOutlet.contentGhost;
                            }),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
          SizedBox(height: 40),
          SizedBox(
            height: 44,
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _selectedProblem != null
                  ? () {
                      controller.setLoadingValue(true);
                      controller
                          .reportProblem(_selectedProblem!, widget.idProduto)
                          .then((value) {
                            controller.registrarReport(
                              widget.idProduto.toString(),
                            );
                            if (context.mounted) {
                              controller.setLoadingValue(false);
                              Modular.to.pop();
                              if (context.mounted) {
                                CustomSnackBar.show(
                                  justTheBottom: widget.emCimaOuEmbaixo,
                                  context: context,
                                  message: value
                                      ? "Problema enviado. Agradecemos seu relato."
                                      : "Ocorreu um erro ao enviar o problema. Tente novamente mais tarde.",
                                  icon: value
                                      ? SvgIcons.feedbackCheck
                                      : SvgIcons.feedbackCaution,
                                );
                              }
                            }
                          });
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorOutlet.contentPrimary,
                disabledBackgroundColor: ColorOutlet.contentDisabled,
                overlayColor: ColorOutlet.systemBorderDisabled.withValues(
                  alpha: 0.3,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: controller.isLoading
                  ? SizedBox(
                      height: 24,
                      width: 24,
                      child: CircularProgressIndicator(
                        color: ColorOutlet.surface,
                        strokeWidth: 2,
                      ),
                    )
                  : TextPattern.customText(
                      text: "Enviar problema",
                      fontSize: 14,
                      color: _selectedProblem != null
                          ? ColorOutlet.surface
                          : ColorOutlet.contentTertiary,
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
