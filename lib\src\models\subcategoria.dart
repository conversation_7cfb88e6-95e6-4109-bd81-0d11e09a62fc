class Subcategoria {
  final int id; // id único no banco (não usamos para filtrar)
  final int idCategoria; // referencia a categoria_id
  final int idSubcategoria; // ESTE é o ID que devemos usar para buscar
  final String nome;

  Subcategoria({
    required this.id,
    required this.idCategoria,
    required this.idSubcategoria,
    required this.nome,
  });

  factory Subcategoria.fromJson(Map<String, dynamic> json) {
    return Subcategoria(
      id: json['id'],
      idCategoria: json['id_categoria'],
      idSubcategoria: json['id_subcategoria'],
      nome: json['nome'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'id_categoria': idCategoria,
      'id_subcategoria': idSubcategoria,
      'nome': nome,
    };
  }
}
