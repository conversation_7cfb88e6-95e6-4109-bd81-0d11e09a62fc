import 'package:supabase_flutter/supabase_flutter.dart';

import '../../logs/app_logger.dart';
import 'i_connection.dart';

class Connection implements IConnection {
  final SupabaseClient _client;

  Connection({SupabaseClient? client}) : _client = client ?? Supabase.instance.client;

  @override
  Future<List<dynamic>> get({
    required String table,
    Map<String, dynamic> filter = const {},
  }) async {
    try {
      final query = _client.from(table).select();

      if (filter.isNotEmpty) {
        query.eq(filter.keys.first, filter.values.first);
      }

      final data = await query;
      AppLogger.logInfo('Sucesso no Get da tabela: $table');
      return data;
    } catch (e, stackTrace) {
      AppLogger.logError('Falha no GET da tabela: $table', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<dynamic>> post({
    required String table,
    required Map<String, dynamic> body,
    String? filter,
  }) async {
    try {
      AppLogger.logInfo('Tentando POST em $table com dados: $body');
      final query = _client.from(table);
      final data = await query.insert(body).select();

      AppLogger.logInfo('Sucesso no POST da tabela: $table');
      return data;
    } catch (e, stackTrace) {
      AppLogger.logError('Falha no POST da tabela: $table', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<dynamic>> put({
    required String table,
    required Map<String, dynamic> body,
    required Map<String, dynamic> filter,
  }) async {
    try {
      final query = _client.from(table);
      final data = await query.update(body).eq(filter.keys.first, filter.values.first).select();

      AppLogger.logInfo('Sucesso no PUT da tabela: $table');
      return data;
    } catch (e, stackTrace) {
      AppLogger.logError('Falha no PUT da tabela: $table', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<dynamic>> delete({
    required String table,
    required Map<String, dynamic> filter,
  }) async {
    try {
      final query = _client.from(table);
      final data = await query.delete().eq(filter.keys.first, filter.values.first).select();

      AppLogger.logInfo('Sucesso no DELETE da tabela: $table');
      return data;
    } catch (e, stackTrace) {
      AppLogger.logError('Falha no DELETE da tabela: $table', e, stackTrace);
      rethrow;
    }
  }
}
