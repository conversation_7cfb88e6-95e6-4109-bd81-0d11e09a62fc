import '../../../../../../theme/color_outlet.dart';
import '../../../controllers/profile_controller.dart';
import 'profile_avatar.dart';
import 'select_image_source.dart';
import 'user_profile_card.dart';
import 'user_profile_card_editing.dart';
import '../../../../../services/supabase/utils/format_date.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class ExpandableProfileCard extends StatelessWidget {
  const ExpandableProfileCard({super.key, required this.controller});

  final ProfileController controller;

  @override
  Widget build(BuildContext context) {
    double sizeTop = MediaQuery.of(context).padding.top;

    double collapsedHeight = 194;
    double isImcompleteProfile = 336;

    const double baseHeight = 700; // altura mínima sem balão
    const double nameBubbleHeight = 100;
    const double birthDateBubbleHeight = 100;
    const double genderBubbleHeight = 100;

    double expandedHeight = baseHeight;

    if (controller.userName == null || controller.userName!.isEmpty) {
      expandedHeight += nameBubbleHeight;
    }
    if (controller.birthDate == null || controller.birthDate!.isEmpty) {
      expandedHeight += birthDateBubbleHeight;
    }
    if (controller.gender == null || controller.gender!.isEmpty) {
      expandedHeight += genderBubbleHeight;
    }

    return Stack(
      alignment: Alignment.topCenter,
      clipBehavior: Clip.none,
      children: [
        Visibility(
          visible: controller.imageFile != null,
          child: Container(
            height: 250,
            width: double.infinity,
            decoration: BoxDecoration(
              color: ColorOutlet.systemBorderDisabled,
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(
            left: 16,
            right: 16,
            top: sizeTop + 44,
          ),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: controller.isEditing
                ? expandedHeight
                : controller.isCompletedProfile(
                    name: controller.userName ?? '',
                    birthDate: controller.birthDate ?? '',
                    gender: controller.gender ?? '',
                  )
                ? collapsedHeight
                : isImcompleteProfile,
            decoration: BoxDecoration(
              color: ColorOutlet.paper,
              borderRadius: BorderRadius.circular(24),
            ),
            padding: EdgeInsets.only(
              left: 24,
              right: 24,
              bottom: 24,
              top: controller.isEditing ? 72 : 56,
            ),
            child: controller.isEditing
                ? UserProfileCardEditing(
                    onPressed: controller.cancelEditing,
                    name: controller.userName ?? '',
                    email: controller.userEmail ?? '',
                    surname: controller.userSurname ?? '',
                    birthDate: controller.birthDate ?? '',
                    joinedDate: formatJoinedDate(
                      controller.userJoinedDate,
                    ),
                    onTapImage: () => showModalBottomSheet(
                      context: context,
                      builder: (context) => SelectImageSource(
                        controller: controller,
                      ),
                    ),
                  )
                : UserProfileCard(
                    isCompletedProfile: controller.isCompletedProfile(
                      name: controller.userName ?? '',
                      birthDate: controller.birthDate ?? '',
                      gender: controller.gender ?? '',
                    ),
                    onPressed: controller.toggleEditing,
                    name: controller.nameAndSurname(),
                    joinedDate: controller.userJoinedDate?.isNotEmpty == true
                        ? DateFormat('dd/MM/yyyy').format(
                            DateTime.tryParse(
                                  controller.userJoinedDate!,
                                ) ??
                                DateTime.now(),
                          )
                        : '',
                  ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: sizeTop + 4),
          child: ProfileAvatar(
            isEditing: controller.isEditing,
            image: controller.userImage ?? '',
            controller: controller,
            onTap: () => showModalBottomSheet(
              context: context,
              builder: (context) => SelectImageSource(controller: controller),
            ),
            onTapImage: () => showModalBottomSheet(
              context: context,
              builder: (context) => SelectImageSource(controller: controller),
            ),
          ),
        ),
      ],
    );
  }
}
