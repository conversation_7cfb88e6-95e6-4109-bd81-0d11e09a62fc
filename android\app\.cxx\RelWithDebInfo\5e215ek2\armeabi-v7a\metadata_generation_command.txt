                        -HH:\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=H:\Android\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=H:\Android\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=H:\Android\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=H:\Android\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=H:\projetos\chegou mercado\promobell\build\app\intermediates\cxx\RelWithDebInfo\5e215ek2\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=H:\projetos\chegou mercado\promobell\build\app\intermediates\cxx\RelWithDebInfo\5e215ek2\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BH:\projetos\chegou mercado\promobell\android\app\.cxx\RelWithDebInfo\5e215ek2\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2