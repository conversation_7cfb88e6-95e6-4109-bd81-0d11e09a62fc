import 'package:flutter/material.dart';

class BlackAndWhiteFilter extends StatelessWidget {
  final Widget child;

  const BlackAndWhiteFilter({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return ColorFiltered(
      colorFilter: const ColorFilter.matrix([
        0.2126,
        0.7152,
        0.0722,
        0,
        0,
        0.2126,
        0.7152,
        0.0722,
        0,
        0,
        0.2126,
        0.7152,
        0.0722,
        0,
        0,
        0,
        0,
        0,
        15,
        0,
      ]),
      child: child,
    );
  }
}
