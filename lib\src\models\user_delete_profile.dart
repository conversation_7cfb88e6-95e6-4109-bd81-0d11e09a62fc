import 'dart:convert';

class UserDeleteProfile {
  String? name;
  String? email;
  String? phone;
  String? image;
  String? imageId;
  String? accessToken;
  DateTime? joinedDate;
  String? tokenFirebase;
  String? razonDelete;
  String? optionalReason;

  UserDeleteProfile({
    this.name,
    this.email,
    this.phone,
    this.image,
    this.imageId,
    this.accessToken,
    this.joinedDate,
    this.tokenFirebase,
    this.razonDelete,
    this.optionalReason,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'name': name,
      'email': email,
      'phone': phone,
      'image': image,
      'imageId': imageId,
      'accessToken': accessToken,
      'joinedDate': joinedDate?.toIso8601String(),
      'tokenFirebase': tokenFirebase,
      'razonDelete': razonDelete,
      'optionalReason': optionalReason,
    };
  }

  factory UserDeleteProfile.fromMap(Map<String, dynamic> map) {
    return UserDeleteProfile(
      name: map['name'] != null ? map['name'] as String : null,
      email: map['email'] != null ? map['email'] as String : null,
      phone: map['phone'] != null ? map['phone'] as String : null,
      image: map['image'] != null ? map['image'] as String : null,
      imageId: map['imageId'] != null ? map['imageId'] as String : null,
      accessToken: map['accessToken'] != null
          ? map['accessToken'] as String
          : null,
      joinedDate: map['joinedDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['joinedDate'] as int)
          : null,
      tokenFirebase: map['tokenFirebase'] != null
          ? map['tokenFirebase'] as String
          : null,
      razonDelete: map['razonDelete'] != null
          ? map['razonDelete'] as String
          : null,
      optionalReason: map['optionalReason'] != null
          ? map['optionalReason'] as String
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory UserDeleteProfile.fromJson(String source) =>
      UserDeleteProfile.fromMap(json.decode(source) as Map<String, dynamic>);

  factory UserDeleteProfile.empty() {
    return UserDeleteProfile(
      name: '',
      email: '',
      phone: '',
      image: '',
      imageId: '',
      accessToken: '',
      joinedDate: DateTime.now(),
      tokenFirebase: '',
      razonDelete: '',
      optionalReason: '',
    );
  }
}
