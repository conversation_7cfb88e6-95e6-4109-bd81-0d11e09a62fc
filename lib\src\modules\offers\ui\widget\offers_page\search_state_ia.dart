import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/theme/svg_icons.dart';

class SearchStateIA extends StatelessWidget {
  const SearchStateIA({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 24),
          Lottie.asset(
            'assets/lottie/promobell-IA-search.json',
            width: 120,
            height: 120,
            fit: BoxFit.cover,
          ),
          const SizedBox(height: 24),

          SvgPicture.asset(SvgIcons.encontreAiLarge),
          const SizedBox(height: 16),
          SizedBox(
            width: 300,
            child: TextPattern.customText(
              text:
                  'Aguarde enquanto nossa busca inteligente encontra a melhor oferta para você.',
              fontSize: 14,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
