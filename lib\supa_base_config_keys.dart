class SupaBaseConfigKeys {
  final String apiUrl;
  final String apiAnonKey;
  final String apiServiceRoles;
  final String googleWebClientId;
  final String androidClientId;
  final String iosClientId;
  final String gcpProjectId;

  SupaBaseConfigKeys({
    required this.apiUrl,
    required this.apiAnonKey,
    required this.apiServiceRoles,
    required this.googleWebClientId,
    required this.androidClientId,
    required this.iosClientId,
    required this.gcpProjectId,
  });

  factory SupaBaseConfigKeys.fromMap(Map<String, dynamic> map) {
    return SupaBaseConfigKeys(
      apiUrl: map['API_URL'],
      apiAnonKey: map['API_ANON_KEY'],
      apiServiceRoles: map['API_SERVICE_ROLES'],
      googleWebClientId: map['GOOGLE_WEB_CLIENT_ID'],
      androidClientId: map['ANDROID_CLIENT_ID'],
      iosClientId: map['IOS_CLIENT_ID'],
      gcpProjectId: map['GCP_PROJECT_ID'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'API_URL': apiUrl,
      'API_ANON_KEY': apiAnonKey,
      'API_SERVICE_ROLES': apiServiceRoles,
      'GOOGLE_WEB_CLIENT_ID': googleWebClientId,
      'ANDROID_CLIENT_ID': androidClientId,
      'IOS_CLIENT_ID': iosClientId,
      'GCP_PROJECT_ID': gcpProjectId,
    };
  }
}
